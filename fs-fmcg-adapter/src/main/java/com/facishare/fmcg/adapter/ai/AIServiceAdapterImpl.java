package com.facishare.fmcg.adapter.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.AIDetectRuleDto;
import com.facishare.ai.detector.api.dto.ApplicationDto;
import com.facishare.ai.detector.api.dto.ModelDto;
import com.facishare.ai.detector.api.dto.ObjectMapDto;
import com.facishare.ai.detector.api.dto.arg.AddApplicationArg;
import com.facishare.ai.detector.api.dto.arg.AddFaceArg;
import com.facishare.ai.detector.api.dto.arg.BatchAddObjectMapArg;
import com.facishare.ai.detector.api.dto.arg.BatchDetectArg;
import com.facishare.ai.detector.api.dto.arg.BatchUpdateObjectMapArg;
import com.facishare.ai.detector.api.dto.arg.DeleteAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.DeleteModelMappingArg;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.FaceComparisionArg;
import com.facishare.ai.detector.api.dto.arg.FaceDetectArg;
import com.facishare.ai.detector.api.dto.arg.GetAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.GetAIDetectRuleListArg;
import com.facishare.ai.detector.api.dto.arg.GetApplicationArg;
import com.facishare.ai.detector.api.dto.arg.GetModelByIdArg;
import com.facishare.ai.detector.api.dto.arg.GetModelListArg;
import com.facishare.ai.detector.api.dto.arg.IdCardDetectArg;
import com.facishare.ai.detector.api.dto.arg.ModelSwitchArg;
import com.facishare.ai.detector.api.dto.arg.OverlayUpdateModelArg;
import com.facishare.ai.detector.api.dto.arg.QueryBalanceOfObjectDetectArg;
import com.facishare.ai.detector.api.dto.arg.QueryObjectMapByIdsArg;
import com.facishare.ai.detector.api.dto.arg.QueryObjectMapByKeysArg;
import com.facishare.ai.detector.api.dto.arg.QueryObjectMapByModelIdArg;
import com.facishare.ai.detector.api.dto.arg.QueryRulesByIdsArg;
import com.facishare.ai.detector.api.dto.arg.SaveAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.SaveModelArg;
import com.facishare.ai.detector.api.dto.arg.UpdateAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.VATInvoiceDetectArg;
import com.facishare.ai.detector.api.dto.result.AddApplicationResult;
import com.facishare.ai.detector.api.dto.result.AddFaceResult;
import com.facishare.ai.detector.api.dto.result.BatchAddObjectMapResult;
import com.facishare.ai.detector.api.dto.result.BatchDetectResult;
import com.facishare.ai.detector.api.dto.result.BatchUpdateObjectMapResult;
import com.facishare.ai.detector.api.dto.result.DetectResult;
import com.facishare.ai.detector.api.dto.result.FaceComparisionResult;
import com.facishare.ai.detector.api.dto.result.FaceDetectResult;
import com.facishare.ai.detector.api.dto.result.GetAIDetectRuleListResult;
import com.facishare.ai.detector.api.dto.result.GetAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.GetApplicationResult;
import com.facishare.ai.detector.api.dto.result.GetModelByIdResult;
import com.facishare.ai.detector.api.dto.result.GetModelListResult;
import com.facishare.ai.detector.api.dto.result.IdCardDetectResult;
import com.facishare.ai.detector.api.dto.result.OverlayUpdateModelResult;
import com.facishare.ai.detector.api.dto.result.QueryBalanceOfObjectDetectResult;
import com.facishare.ai.detector.api.dto.result.QueryObjectMapByIdsResult;
import com.facishare.ai.detector.api.dto.result.QueryObjectMapByKeysResult;
import com.facishare.ai.detector.api.dto.result.QueryObjectMapByModelIdResult;
import com.facishare.ai.detector.api.dto.result.QueryRulesByIdsResult;
import com.facishare.ai.detector.api.dto.result.SaveAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.SaveModelResult;
import com.facishare.ai.detector.api.dto.result.UpdateAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.VATInvoiceDetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.AICostService;
import com.facishare.ai.detector.api.service.AIDetectRuleService;
import com.facishare.ai.detector.api.service.ApplicationService;
import com.facishare.ai.detector.api.service.DetectorService;
import com.facishare.ai.detector.api.service.FaceDetectService;
import com.facishare.ai.detector.api.service.ModelService;
import com.facishare.ai.detector.api.service.ObjectMapService;
import com.facishare.ai.detector.api.service.OcrService;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.ai.dto.AddFaceRst;
import com.facishare.fmcg.adapter.ai.dto.BatchDetectRst;
import com.facishare.fmcg.adapter.ai.dto.DetectRst;
import com.facishare.fmcg.adapter.ai.dto.FaceComparisionRst;
import com.facishare.fmcg.adapter.ai.dto.FaceDetectRst;
import com.facishare.fmcg.adapter.ai.dto.IdCardDetectRst;
import com.facishare.fmcg.adapter.ai.dto.ObjectDto;
import com.facishare.fmcg.adapter.ai.dto.QueryFeeDto;
import com.facishare.fmcg.adapter.ai.dto.VATInvoiceDetectRst;
import com.facishare.fmcg.adapter.common.HttpUtil;
import com.facishare.fmcg.adapter.file.FileAdapter;
import com.facishare.fmcg.adapter.organization.OrganizationAdapter;
import com.facishare.fmcg.adapter.util.ConvertUtil;
import com.facishare.fmcg.api.dto.ai.detect.RealNameFaceAuth;
import com.facishare.fmcg.api.dto.ai.model.AIDetectRuleDTO;
import com.facishare.fmcg.api.dto.ai.model.FieldDTO;
import com.facishare.fmcg.api.dto.ai.model.GetModelById;
import com.facishare.fmcg.api.dto.ai.model.ModelDTO;
import com.facishare.fmcg.api.dto.ai.model.ObjectMapDTO;
import com.facishare.fmcg.api.dto.ai.model.TokenInfoDTO;
import com.facishare.fmcg.api.dto.common.UserInfo;
import com.facishare.fmcg.api.error.ErrorCode;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.util.I18N;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fs.fmcg.sdk.ai.plat.*;
import com.google.common.collect.Lists;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020/6/1 下午2:51
 */
@Service(value = "aiServiceAdapter")
@Slf4j
public class AIServiceAdapterImpl implements AIServiceAdapter {

    public static List<String> COLORS = Lists.newArrayList("f92a2a", "43d9d9", "f96464", "20dada", "fce0e0", "ff7474", "6da9a9", "1f8383", "f6acac", "b9cfcf",
            "508f8f", "c91f1f", "ffa9a9", "e05858", "3f3f3f", "f2b6b6", "86c1c1", "9b3939", "b17777", "938585",
            "189797", "d25555", "247070", "b75c5c", "3dd4d4");

    @Resource
    private FaceDetectService faceDetectService;

    @Resource
    private OcrService ocrService;

    @Resource
    private AICostService aiCostService;

    @Resource
    private DetectorService detectorService;

    @Resource
    private TokenFactory tokenFactory;

    @Resource
    private FileAdapter fileAdapter;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private ModelService modelService;

    @Resource
    private OrganizationAdapter organizationAdapter;

    @Resource
    private ObjectMapService objectMapService;

    @Resource
    private AIDetectRuleService aiDetectRuleService;


    @Override
    public QueryFeeDto queryFee(int tenantId, String modelId, String type) {
        QueryBalanceOfObjectDetectArg aiArg = new QueryBalanceOfObjectDetectArg();
        aiArg.setModelId(modelId);
        aiArg.setTenantId(tenantId);
        aiArg.setType(type);
        QueryBalanceOfObjectDetectResult result = aiCostService.queryBalanceOfObjectDetect(aiArg);
        QueryFeeDto queryFeeDto = new QueryFeeDto();
        queryFeeDto.setBalance(result.getBalance());
        queryFeeDto.setCost(result.getCost());
        queryFeeDto.setDetectedPicCnt(result.getDetectedPicCnt());
        return queryFeeDto;
    }

    @Override
    public IdCardDetectRst idCardDetect(String tenantAccount, String imagePath, String cardSide, String detectDirection,
                                        String detectRisk, String detectPhoto, String detectRectify) throws AiProviderException, IOException {
        IdCardDetectArg arg = new IdCardDetectArg();
        arg.setTenantAccount(tenantAccount);
        arg.setImagePath(imagePath);
        arg.setCardSide(cardSide);
        arg.setDetectDirection(detectDirection);
        arg.setDetectPhoto(detectPhoto);
        arg.setDetectRisk(detectRisk);
        arg.setDetectRectify(detectRectify);
        IdCardDetectResult result = ocrService.idCardDetect(arg);
        IdCardDetectRst rst = new IdCardDetectRst();
        BeanUtils.copyProperties(result, rst);
        return rst;
    }

    @Override
    public VATInvoiceDetectRst vatInvoiceDetect(String tenantAccount, String imagePath, String accuracy, String type)
            throws AiProviderException, IOException {
        VATInvoiceDetectArg arg = new VATInvoiceDetectArg();
        arg.setImagePath(imagePath);
        arg.setTenantAccount(tenantAccount);
        arg.setAccuracy(accuracy);
        arg.setType(type);
        VATInvoiceDetectResult result = ocrService.vatInvoiceDetect(arg);
        VATInvoiceDetectRst rst = new VATInvoiceDetectRst();
        BeanUtils.copyProperties(result, rst);
        return rst;
    }

    @Override
    public AddFaceRst addFace(int tenantId, String tenantAccount, String imagePath, String userId, String faceToken)
            throws AiProviderException {
        AddFaceArg addFaceArg = new AddFaceArg();
        addFaceArg.setImagePath(imagePath);
        addFaceArg.setTenantAccount(tenantAccount);
        addFaceArg.setTenantId(tenantId);
        addFaceArg.setUserId(userId);
        addFaceArg.setFaceToken(faceToken);
        AddFaceResult result = faceDetectService.addFace(addFaceArg);
        AddFaceRst rst = new AddFaceRst();
        BeanUtils.copyProperties(result, rst);
        return rst;
    }

    @Override
    public FaceComparisionRst faceComparision(int tenantId, String tenantAccount, String userId, String baseImage,
                                              String detectedImage) throws AiProviderException {
        FaceComparisionArg arg = new FaceComparisionArg();
        arg.setDetectedImagePath(detectedImage);
        arg.setBaseImagePath(baseImage);
        arg.setTenantAccount(tenantAccount);
        arg.setTenantId(tenantId);
        arg.setUserId(userId);
        FaceComparisionResult result = faceDetectService.faceComparision(arg);
        FaceComparisionRst faceComparisionRst = new FaceComparisionRst();
        BeanUtils.copyProperties(result, faceComparisionRst);
        return faceComparisionRst;
    }

    @Override
    public DetectRst detect(int tenantId, String tenantAccount, String appId, int userId, String modelId, String path)
            throws AiProviderException {
        DetectArg arg = new DetectArg();
        arg.setModelId(modelId);
        arg.setUserId(String.valueOf(userId));
        arg.setAppId(appId);
        arg.setTenantId(String.valueOf(tenantId));
        arg.setTenantAccount(tenantAccount);
        arg.setPath(path);
        DetectResult result = detectorService.detect(arg);
        return transform(result);
    }

    @Override
    public BatchDetectRst batchDetect(int tenantId, String tenantAccount, String appId, int userId, String modelId,
                                      List<String> paths) throws AiProviderException {
        BatchDetectArg arg = new BatchDetectArg();
        arg.setModelId(modelId);
        arg.setUserId(String.valueOf(userId));
        arg.setAppId(appId);
        arg.setTenantId(String.valueOf(tenantId));
        arg.setTenantAccount(tenantAccount);
        arg.setPaths(paths);
        BatchDetectResult result = detectorService.batchDetect(arg);
        BatchDetectRst rst = new BatchDetectRst();
        rst.setResults(new ArrayList<>());
        if (!CollectionUtils.isEmpty(result.getResults())) {
            result.getResults().forEach(v -> {
                rst.getResults().add(transform(v));
            });
        }
        return rst;
    }

    @Override
    public FaceDetectRst faceDetect(int tenantId, String tenantAccount, String userId, String image)
            throws AiProviderException {
        FaceDetectArg arg = new FaceDetectArg();
        arg.setImagePath(image);
        arg.setTenantAccount(tenantAccount);
        arg.setTenantId(tenantId);
        arg.setUserId(userId);
        FaceDetectResult result = faceDetectService.faceDetect(arg);
        return JSON.parseObject(JSON.toJSONString(result), FaceDetectRst.class);
    }

    @Override
    public RealNameFaceAuth.Result realNameFaceAuth(RealNameFaceAuth.Arg arg) throws FmcgException {
        String url = "https://aip.baidubce.com/rest/2.0/face/v4/mingjing/verify?access_token="
                + tokenFactory.getToken(AppEnum.BAIDU_FXIAOKERD_FACE.value());
        String ea = eieaConverter.enterpriseIdToAccount(arg.getTenantId());
        JSONObject params = new JSONObject();
        params.put("id_card_number", arg.getIdCardNumber());
        params.put("name", arg.getName());
        params.put("image",
                Base64.getEncoder().encodeToString(fileAdapter.downloadAllByte(ea, -10000, arg.getNPath())));
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        try {
            log.info("arg：{}", arg);
            JSONObject result = HttpUtil.post(url, headers, params, JSONObject.class);
            log.info("result:{}", result);
            if (result.containsKey("error_code") && result.getInteger("error_code") != 0) {
                log.info("实名认证失败：{}", result);
                throw new FmcgException(result.getString("error_msg"), result.getInteger("error_code"));
            }
            RealNameFaceAuth.Result rst = new RealNameFaceAuth.Result();
            rst.setLogId(result.getString("log_id"));
            JSONObject detectRst = result.getJSONObject("result");
            if (detectRst != null) {
                rst.setScore(detectRst.getDouble("score"));
                rst.setVerifyStatus(detectRst.getInteger("verify_status"));
                if (rst.getVerifyStatus() != 0) {
                    String msg = "";
                    if (rst.getVerifyStatus() == 1) {
                        msg = "身份证号与姓名不匹配或该身份证号不存在";
                    } else if (rst.getVerifyStatus() == 2) {
                        msg = "公安网图片不存在或质量过低";
                    }
                    throw new FmcgException(msg, rst.getVerifyStatus());
                }
            }
            return rst;
        } catch (IOException e) {
            log.info("detect error", e);
            throw new FmcgException(ErrorCode.REAL_NAME_AUTH_ERROR);
        }
    }

    DetectRst transform(DetectResult result) {
        DetectRst rst = new DetectRst();
        BeanUtils.copyProperties(result, rst);
        if (!CollectionUtils.isEmpty(result.getObjects())) {
            List<ObjectDto> objs = new ArrayList<>();
            result.getObjects().forEach(v -> {
                ObjectDto obj = new ObjectDto();
                BeanUtils.copyProperties(v, obj);
                objs.add(obj);
            });
            rst.setObjects(objs);
        }
        return rst;
    }

    private TokenInfoDTO transformToTokenInfoDTO(ApplicationDto applicationDto) {
        if (applicationDto == null) {
            return null;
        }

        TokenInfoDTO tokenInfoDTO = new TokenInfoDTO();
        String platform = applicationDto.getPlatForm();

        if (platform == null) {
            return tokenInfoDTO;
        }

        TokenPlatFormEnum tokenPlatFormEnum = TokenPlatFormEnum.get(platform);
        if (tokenPlatFormEnum == null) {
            log.warn("未知平台类型: {}", platform);
            return tokenInfoDTO;
        }

        return buildTokenInfoDTO(applicationDto, tokenPlatFormEnum);
    }

    private TokenInfoDTO buildTokenInfoDTO(ApplicationDto applicationDto, TokenPlatFormEnum platformEnum) {
        TokenInfoDTO tokenInfoDTO = new TokenInfoDTO();
        tokenInfoDTO.setIdentityKey(applicationDto.getIdentityKey());
        tokenInfoDTO.setType(applicationDto.getPlatForm());

        Map<String, Object> params = applicationDto.getParams();
        if (params == null) {
            return tokenInfoDTO;
        }

        switch (platformEnum) {
            case BAIDU:
                fillBaiduTokenInfo(tokenInfoDTO, params);
                break;
            case HUAWEI:
                fillHuaweiTokenInfo(tokenInfoDTO, params);
                break;
            case SENSE_TIME:
                tokenInfoDTO.setAk((String) params.get("ak"));
                tokenInfoDTO.setSk((String) params.get("sk"));
                tokenInfoDTO.setHost((String) params.get("host"));
                break;
            case YQSL:
                tokenInfoDTO.setAppId((String) params.get("appId"));
                tokenInfoDTO.setKey((String) params.get("key"));
                break;
            case TU_JIANG:
                tokenInfoDTO.setAccount((String) params.get("account"));
                tokenInfoDTO.setPassword((String) params.get("password"));
                tokenInfoDTO.setCompCode((String) params.get("compCode"));
                tokenInfoDTO.setSecretKey((String) params.get("secretKey"));
                tokenInfoDTO.setUrl((String) params.get("url"));
                break;
            case WECHAT:
                tokenInfoDTO.setGrantType((String) params.get("grantType"));
                tokenInfoDTO.setSecret((String) params.get("secret"));
                tokenInfoDTO.setAppId((String) params.get("appId"));
                break;
            case TENCENT:
                tokenInfoDTO.setSecretId((String) params.get("secretId"));
                tokenInfoDTO.setSecretKey((String) params.get("secretKey"));
                break;
            case DEFAULT:
                tokenInfoDTO.setToken((String) params.get("token"));
                break;
            case RIO:
                tokenInfoDTO.setToken((String) params.get("token"));
                break;
            default:
                log.warn("未处理的平台类型: {}", platformEnum);
        }

        return tokenInfoDTO;
    }

    private void fillBaiduTokenInfo(TokenInfoDTO tokenInfoDTO, Map<String, Object> params) {
        tokenInfoDTO.setAppKey((String) params.get("appKey"));
        tokenInfoDTO.setSecretKey((String) params.get("secretKey"));
    }

    private void fillHuaweiTokenInfo(TokenInfoDTO tokenInfoDTO, Map<String, Object> params) {
        tokenInfoDTO.setUserName((String) params.get("userName"));
        tokenInfoDTO.setPassword((String) params.get("password"));
        tokenInfoDTO.setDomainName((String) params.get("domainName"));
        tokenInfoDTO.setProjectName((String) params.get("projectName"));
    }

    @Override
    public TokenInfoDTO getTokenInfo(Integer tenantId, String identityKey) {
        GetApplicationArg arg = new GetApplicationArg();
        arg.setTenantId(tenantId);
        arg.setIdentityKey(identityKey);
        GetApplicationResult result = applicationService.getApplication(arg);
        if (result == null || result.getData() == null) {
            return null;
        }
        return transformToTokenInfoDTO(result.getData());
    }

    private ApplicationDto transformToApplicationDto(TokenInfoDTO tokenInfoDTO) {
        ApplicationDto applicationDto = new ApplicationDto();
        applicationDto.setIdentityKey(tokenInfoDTO.getIdentityKey());
        applicationDto.setPlatForm(tokenInfoDTO.getType());

        Map<String, Object> params = new HashMap<>();
        TokenPlatFormEnum tokenPlatFormEnum = TokenPlatFormEnum.get(tokenInfoDTO.getType());

        if (tokenPlatFormEnum == null) {
            log.warn("Unknown platform: {}", tokenInfoDTO.getType());
            return applicationDto;
        }

        switch (tokenPlatFormEnum) {
            case BAIDU:
                params.put("appKey", tokenInfoDTO.getAppKey());
                params.put("secretKey", tokenInfoDTO.getSecretKey());
                break;

            case HUAWEI:
                params.put("userName", tokenInfoDTO.getUserName());
                params.put("password", tokenInfoDTO.getPassword());
                params.put("domainName", tokenInfoDTO.getDomainName());
                params.put("projectName", tokenInfoDTO.getProjectName());
                break;

            case SENSE_TIME:
                params.put("ak", tokenInfoDTO.getAk());
                params.put("sk", tokenInfoDTO.getSk());
                params.put("host", tokenInfoDTO.getHost());
                break;

            case YQSL:
                params.put("appId", tokenInfoDTO.getAppId());
                params.put("key", tokenInfoDTO.getKey());
                break;

            case TU_JIANG:
                params.put("account", tokenInfoDTO.getAccount());
                params.put("password", tokenInfoDTO.getPassword());
                params.put("compCode", tokenInfoDTO.getCompCode());
                params.put("secretKey", tokenInfoDTO.getSecretKey());
                params.put("url", tokenInfoDTO.getUrl());
                break;

            case WECHAT:
                params.put("grantType", tokenInfoDTO.getGrantType());
                params.put("secret", tokenInfoDTO.getSecret());
                params.put("appId", tokenInfoDTO.getAppId());
                break;

            case TENCENT:
                params.put("secretId", tokenInfoDTO.getSecretId());
                params.put("secretKey", tokenInfoDTO.getSecretKey());
                break;

            case DEFAULT:
                params.put("token", tokenInfoDTO.getToken());
                break;

            case RIO:
                params.put("token", tokenInfoDTO.getToken());
                break;

            default:
                log.warn("Unhandled platform enum: {}", tokenPlatFormEnum);
                break;
        }

        applicationDto.setParams(params);
        return applicationDto;
    }

    @Override
    public TokenInfoDTO addTokenInfo(Integer tenantId, TokenInfoDTO tokenInfoDTO) {
        if (tokenInfoDTO == null || tenantId == null) {
            return null;
        }


        validateTokenInfoIsValid(tokenInfoDTO);

        // 转换为ApplicationDto
        ApplicationDto applicationDto = transformToApplicationDto(tokenInfoDTO);
        applicationDto.setTenantId(tenantId);
        // 构建保存参数
        AddApplicationArg arg = new AddApplicationArg();
        arg.setTenantId(tenantId);
        arg.setApplication(applicationDto);

        // 调用服务保存
        AddApplicationResult result = applicationService.addApplication(arg);
        if (result == null || result.getApplication() == null) {
            return null;
        }

        // 转换回TokenInfoDTO并返回
        return transformToTokenInfoDTO(result.getApplication());
    }

    private void validateTokenInfoIsValid(TokenInfoDTO tokenInfoDTO) {
        JSONObject datum = JSON.parseObject(JSON.toJSONString(tokenInfoDTO));
        TokenPlatFormEnum tokenPlatFormEnum = TokenPlatFormEnum.get(datum.getString("type"));
        Application application = null;
        switch (tokenPlatFormEnum) {
            case BAIDU:
                application = datum.toJavaObject(BaiduApplication.class);
                break;
            case HUAWEI:
                application = datum.toJavaObject(HuaWeiApplication.class);
                break;
            case RIO:
                application = datum.toJavaObject(RioApplication.class);
                break;
            case WECHAT:
                application = datum.toJavaObject(WechatApplication.class);
                break;
            case TU_JIANG:
                application = datum.toJavaObject(TuJiangApplication.class);
                break;
            case TENCENT:
                application = datum.toJavaObject(TencentApplication.class);
                break;
            case YQSL:
                application = datum.toJavaObject(YqslApplication.class);
                break;
            case SENSE_TIME:
                application = datum.toJavaObject(SenseTimeApplication.class);
                break;
            default:
                application = datum.toJavaObject(DefaultApplication.class);
                break;
        }
        try {
            application.refreshToken();
        } catch (Exception e) {
            log.info("校验失败", e);
            throw new FmcgException(ErrorCode.TOKEN_VALIDATION_ERROR);
        }

    }

    @Override
    public List<ModelDTO> getModelList(Integer tenantId, String scene) {
        GetModelListArg arg = new GetModelListArg();
        arg.setTenantId(tenantId);
        arg.setScene(scene);
        GetModelListResult result = modelService.getModelList(arg);

        if (result == null || CollectionUtils.isEmpty(result.getModels())) {
            return new ArrayList<>();
        }
        // 收集所有非空的创建人和修改人ID
        Map<Integer, UserInfo> userNameMap = getEmployeeMap(tenantId, result.getModels());

        return result.getModels().stream().map(modelDto -> {
            ModelDTO dto = new ModelDTO();
            BeanUtils.copyProperties(modelDto, dto);
            fillTokenInfo(tenantId, modelDto.getTokenKey(), dto, true);
            dto.setCreator(userNameMap.get(modelDto.getCreator()));
            dto.setLastModifier(userNameMap.get(modelDto.getLastModifier()));
            return dto;
        }).collect(Collectors.toList());
    }

    private Map<Integer, UserInfo> getEmployeeMap(Integer tenantId, List<ModelDto> modelDTOS) {
        Set<Integer> employeeIds = modelDTOS.stream()
                .filter(model -> model != null)
                .flatMap(model -> {
                    Set<Integer> ids = new HashSet<>();
                    if (model.getCreator() != null) {
                        ids.add(model.getCreator());
                    }
                    if (model.getLastModifier() != null) {
                        ids.add(model.getLastModifier());
                    }
                    return ids.stream();
                })
                .collect(Collectors.toSet());

        return getUserInfoMap(tenantId, employeeIds);
    }

    /**
     * 获取用户信息映射
     *
     * @param tenantId    租户ID
     * @param employeeIds 员工ID集合
     * @return 员工ID到用户信息的映射
     */
    private Map<Integer, UserInfo> getUserInfoMap(Integer tenantId, Set<Integer> employeeIds) {
        // 查询员工信息并构建id到name的映射
        Map<Integer, UserInfo> userInfoMap = organizationAdapter.queryEmployees(tenantId, new ArrayList<>(employeeIds))
                .stream()
                .collect(Collectors.toMap(
                        EmployeeDto::getEmployeeId,
                        v -> new UserInfo(v.getEmployeeId(), v.getName()),
                        (v1, v2) -> v1
                ));
        userInfoMap.put(-10000, new UserInfo(-10000, I18N.getOrDefault("SYSTEM", "系统")));
        userInfoMap.put(null, new UserInfo(-10000, I18N.getOrDefault("SYSTEM", "系统")));
        return userInfoMap;
    }

    private void fillTokenInfo(Integer tenantId, String identityKey, ModelDTO modelDTO, boolean isMask) {
        if (Strings.isNullOrEmpty(identityKey)) {
            return;
        }
        GetApplicationArg arg = new GetApplicationArg();
        arg.setTenantId(tenantId);
        arg.setIdentityKey(identityKey);
        GetApplicationResult result = applicationService.getApplication(arg);
        if (result.getData() == null) {
            throw new FmcgException(ErrorCode.TOKEN_INFO_ERROR);
        }
        ConvertUtil.fillModelDTOByApplicationDto(modelDTO, result.getData(), isMask);
    }

    @Override
    public ModelDTO addModel(Integer tenantId, Integer userId, ModelDTO modelDTO) {
        if (tenantId == null || modelDTO == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            return doAddModel(tenantId, userId, modelDTO);
        } catch (FmcgException e) {
            log.info("添加模型失败, tenantId={}, modelDTO={}", tenantId, modelDTO, e);
            throw e;
        } catch (Exception e) {
            log.error("添加模型失败, tenantId={}, modelDTO={}", tenantId, modelDTO, e);
            throw new FmcgException(ErrorCode.MODEL_ADD_BUSINESS_ERROR);
        }
    }

    private ModelDTO doAddModel(Integer tenantId, int userId, ModelDTO modelDTO) {
        modelDTO.setTenantId(tenantId);
        long time = System.currentTimeMillis();
        modelDTO.setCreateTime(time);
        modelDTO.setLastModifyTime(time);

        ModelDto modelDto = convertToModelDto(userId, modelDTO);

        SaveModelArg arg = new SaveModelArg();
        arg.setTenantId(tenantId);
        arg.setModel(modelDto);

        SaveModelResult result = modelService.saveModel(arg);
        if (!Strings.isNullOrEmpty(result.getStatus())) {
            throw new FmcgException(I18N.getOrDefaultByOriginalKey(result.getStatus(), result.getMsg()), -1);
        }
        modelDTO.setId(result.getId());
        return modelDTO;
    }

    private ModelDto convertToModelDto(int userId, ModelDTO modelDTO) {
        ModelDto modelDto = new ModelDto();
        BeanUtils.copyProperties(modelDTO, modelDto);
        modelDto.setCreator(userId);
        modelDto.setLastModifier(userId);
        modelDto.setTokenKey(modelDTO.getToken_identityKey());
        return modelDto;
    }

    @Override
    public ModelDTO overloadUpdateModel(Integer tenantId, Integer userId, ModelDTO modelDTO) {

        modelDTO.setLastModifyTime(System.currentTimeMillis());
        ModelDto updateModelDto = new ModelDto();
        BeanUtils.copyProperties(modelDTO, updateModelDto);
        updateModelDto.setCreator(modelDTO.getCreator().getId());
        updateModelDto.setCreateTime(modelDTO.getCreateTime());
        updateModelDto.setLastModifyTime(System.currentTimeMillis());
        updateModelDto.setLastModifier(userId);
        if (!Strings.isNullOrEmpty(modelDTO.getToken_identityKey())) {
            updateModelDto.setTokenKey(modelDTO.getToken_identityKey());
        }

        OverlayUpdateModelArg arg = new OverlayUpdateModelArg();
        arg.setTenantId(tenantId);
        arg.setModel(updateModelDto);
        OverlayUpdateModelResult result = modelService.overlayUpdate(arg);
        if (!Strings.isNullOrEmpty(result.getCode())) {
            throw new FmcgException(I18N.getOrDefaultByOriginalKey(result.getCode(), result.getMessage()), -1);
        }
        ModelDTO resultDTO = new ModelDTO();
        BeanUtils.copyProperties(result.getModel(), resultDTO);
        return resultDTO;
    }

    @Override
    public void modelSwitch(Integer tenantId, String modelId, int modelStatus) {
        if (tenantId == null || StringUtils.isEmpty(modelId)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            ModelSwitchArg arg = new ModelSwitchArg();
            arg.setTenantId(tenantId);
            arg.setId(modelId);
            arg.setSwitchStatus(modelStatus);
            modelService.modelSwitch(arg);
        } catch (Exception e) {
            log.error("切换模型状态失败, tenantId={}, modelId={}, status={}",
                    tenantId, modelId, modelStatus, e);
            throw new FmcgException(ErrorCode.MODEL_STATUS_SWITCH_BUSINESS_ERROR);
        }
    }

    @Override
    public GetModelById.Result getModelById(Integer tenantId, String modelId, boolean needObjectMap, boolean needRule,
                                            boolean needDisplayScene) {
        // 构建查询参数
        GetModelByIdArg arg = new GetModelByIdArg();
        arg.setTenantId(tenantId);
        arg.setModelId(modelId);
        arg.setNeedRule(needRule);
        arg.setNeedObjectMap(needObjectMap);
        arg.setNeedDisplayScene(needDisplayScene);

        // 调用modelService获取模型信息
        GetModelByIdResult modelResult = modelService.getModelById(arg);

        // 构建返回结果
        GetModelById.Result result = new GetModelById.Result();
        if (modelResult != null && modelResult.getModelDto() != null) {
            // 转换为正确的ModelDTO类型
            ModelDTO modelDTO = new ModelDTO();
            BeanUtils.copyProperties(modelResult.getModelDto(), modelDTO);
            result.setModel(modelDTO);
            fillTokenInfo(tenantId, modelResult.getModelDto().getTokenKey(), modelDTO, true);
            Map<Integer, UserInfo> userInfoMap = getEmployeeMap(tenantId, Lists.newArrayList(modelResult.getModelDto()));
            modelDTO.setCreator(userInfoMap.get(modelResult.getModelDto().getCreator()));
            modelDTO.setLastModifier(userInfoMap.get(modelResult.getModelDto().getLastModifier()));
            if (modelDTO.getParams() == null) {
                modelDTO.setParams(new JSONObject());
            }

            // 根据需要设置其他信息
            if (needObjectMap) {
                result.setObjectMaps(
                        modelResult.getObjectMapList().stream().map(this::transform).collect(Collectors.toList()));
            }

            if (needRule) {
                Map<Integer, UserInfo> userInfo2 = getUserInfoByRules(tenantId, modelResult.getRuleList());
                result.setRules(modelResult.getRuleList().stream().map(rule -> this.transform(rule, userInfo2)).collect(Collectors.toList()));
            }

            if (needDisplayScene) {
                result.setDisplayScenes(new ArrayList<>());
            }
        } else {
            throw new FmcgException(ErrorCode.MODEL_NOT_FOUND);
        }

        return result;
    }


    private ObjectMapDTO transform(ObjectMapDto objectMap) {
        if (objectMap == null) {
            return null;
        }
        ObjectMapDTO objectMapDTO = new ObjectMapDTO();
        BeanUtils.copyProperties(objectMap, objectMapDTO);
        objectMapDTO.setObjectName(objectMap.getName());
        return objectMapDTO;
    }

    private AIDetectRuleDTO transform(AIDetectRuleDto rule) {
        if (rule == null) {
            return null;
        }
        AIDetectRuleDTO ruleDTO = new AIDetectRuleDTO();
        BeanUtils.copyProperties(rule, ruleDTO);
        if (rule.getFieldMap() != null) {
            rule.getFieldMap().forEach((k, v) -> {
                if (v == null) {
                    ruleDTO.getFieldMap().put(k, null);
                } else {
                    FieldDTO fieldDTO = new FieldDTO();
                    BeanUtils.copyProperties(v, fieldDTO);
                    ruleDTO.getFieldMap().put(k, fieldDTO);
                }
            });
        }

        return ruleDTO;
    }

    private AIDetectRuleDTO transform(AIDetectRuleDto rule, Map<Integer, UserInfo> userInfoMap) {
        if (rule == null) {
            return null;
        }
        AIDetectRuleDTO ruleDTO = transform(rule);
        ruleDTO.setCreator(userInfoMap.get(rule.getCreator()));
        ruleDTO.setLastModifier(userInfoMap.get(rule.getLastModifier()));
        return ruleDTO;
    }

    @Override
    public List<ObjectMapDTO> getObjectMap(Integer tenantId, String modelId) {
        if (tenantId == null || StringUtils.isEmpty(modelId)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            QueryObjectMapByModelIdArg arg = new QueryObjectMapByModelIdArg();
            arg.setTenantId(tenantId);
            arg.setModelId(modelId);
            QueryObjectMapByModelIdResult result = objectMapService.queryObjectMapByModelIdAndTenantId(arg);

            if (result == null || CollectionUtils.isEmpty(result.getData())) {
                return new ArrayList<>();
            }

            return result.getData().stream()
                    .map(this::transform)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取对象映射失败, tenantId={}, modelId={}", tenantId, modelId, e);
            throw new FmcgException(ErrorCode.OBJECT_MAP_QUERY_BUSINESS_ERROR);
        }
    }

    @Override
    public List<ObjectMapDTO> batchUpdateObjectMapDTO(Integer tenantId, List<ObjectMapDTO> objectMapDTOList) {
        if (tenantId == null || CollectionUtils.isEmpty(objectMapDTOList)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        List<ObjectMapDto> dbObjectDto = queryObjectMapDtoByIds(tenantId, objectMapDTOList.stream().map(ObjectMapDTO::getId).collect(Collectors.toList()));
        Map<String, ObjectMapDTO> objectMapMap = objectMapDTOList.stream()
                .collect(Collectors.toMap(ObjectMapDTO::getId, o -> o, (a, b) -> a));

        for (ObjectMapDto dbData : dbObjectDto) {
            ObjectMapDTO objectMapDTO = objectMapMap.get(dbData.getId());
            // 将 objectMapDTO 的所有字段复制到 dbData
            dbData.setObjectId(objectMapDTO.getObjectId());
            dbData.setName(objectMapDTO.getObjectName());
            dbData.setApiName(objectMapDTO.getApiName());
            dbData.setKey(objectMapDTO.getKey());
            if (dbData.getExtraData() == null) {
                dbData.setExtraData(new JSONObject());
            }
            if (Strings.isNullOrEmpty(objectMapDTO.getUnitType())) {
                dbData.getExtraData().remove("unit_type");
            } else {
                dbData.getExtraData().put("unit_type", objectMapDTO.getUnitType());
            }
            dbData.setModelId(objectMapDTO.getModelId());
            dbData.setTenantId(objectMapDTO.getTenantId());
        }
        try {
            BatchUpdateObjectMapArg arg = new BatchUpdateObjectMapArg();
            arg.setTenantId(tenantId);
            arg.setObjectMapList(dbObjectDto);

            BatchUpdateObjectMapResult result = objectMapService.batchUpdateObjectMap(arg);

            if (result == null || CollectionUtils.isEmpty(result.getObjectMapDtoList())) {
                return new ArrayList<>();
            }

            return result.getObjectMapDtoList().stream()
                    .map(this::transform)
                    .collect(Collectors.toList());
        } catch (FmcgException fmcgException) {
            throw fmcgException;
        } catch (Exception e) {
            log.error("批量更新对象映射失败, tenantId={}, objectMapDTOList={}", tenantId, objectMapDTOList, e);
            throw new FmcgException(ErrorCode.OBJECT_MAP_UPDATE_BUSINESS_ERROR);
        }
    }

    private List<ObjectMapDto> queryObjectMapDtoByIds(Integer tenantId, List<String> objectMapIds) {
        QueryObjectMapByIdsArg arg = new QueryObjectMapByIdsArg();
        arg.setTenantId(tenantId);
        arg.setIds(objectMapIds);
        QueryObjectMapByIdsResult result = objectMapService.queryObjectMapByIds(arg);
        if (result == null || CollectionUtils.isEmpty(result.getObjectMapList())) {
            return Collections.emptyList();
        }
        return result.getObjectMapList();
    }

    @Override
    public void batchDeleteObjectMap(Integer tenantId, List<String> objectMapIdsList) {
        if (tenantId == null || CollectionUtils.isEmpty(objectMapIdsList)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            DeleteModelMappingArg arg = new DeleteModelMappingArg();
            arg.setTenantId(tenantId);
            arg.setUniqueIds(objectMapIdsList);
            objectMapService.deleteModelMapping(arg);
        } catch (Exception e) {
            log.error("批量删除对象映射失败, tenantId={}, objectMapIds={}", tenantId, objectMapIdsList, e);
            throw new FmcgException(ErrorCode.OBJECT_MAP_DELETE_BUSINESS_ERROR);
        }
    }

    @Override
    public List<ObjectMapDTO> batchAddObjectMap(Integer tenantId, List<ObjectMapDTO> objectMapDTOList) {
        if (tenantId == null || CollectionUtils.isEmpty(objectMapDTOList)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            validateObjectKeyExists(tenantId, objectMapDTOList);
            List<ObjectMapDto> saveDtos = objectMapDTOList.stream()
                    .map(dto -> {
                        ObjectMapDto objectMapDto = new ObjectMapDto();
                        BeanUtils.copyProperties(dto, objectMapDto);
                        objectMapDto.setTenantId(tenantId);
                        return objectMapDto;
                    }).collect(Collectors.toList());
            for (int index = 0; index < saveDtos.size(); index++) {
                ObjectMapDto dto = saveDtos.get(index);
                if (dto.getExtraData() == null) {
                    dto.setExtraData(new JSONObject());
                }
                dto.setAppId("CRM");
                dto.setColor(COLORS.get(index % COLORS.size()));
            }
            BatchAddObjectMapArg arg = new BatchAddObjectMapArg();
            arg.setTenantId(tenantId);
            arg.setMaps(saveDtos);

            BatchAddObjectMapResult result = objectMapService.batchAddObjectMap(arg);

            return result.getObjectMapDtoList().stream()
                    .map(this::transform)
                    .collect(Collectors.toList());
        } catch (FmcgException fmcgException) {
            throw fmcgException;
        } catch (Exception e) {
            log.error("批量添加对象映射失败, tenantId={}, objectMapDTOList={}", tenantId, objectMapDTOList, e);
            throw new FmcgException(ErrorCode.OBJECT_MAP_ADD_BUSINESS_ERROR);
        }
    }

    private void validateObjectKeyExists(Integer tenantId, List<ObjectMapDTO> objectMapDTOList) {
        if (tenantId == null || CollectionUtils.isEmpty(objectMapDTOList)) {
            return;
        }

        // 提取所有需要检查的key和modelId
        List<String> keys = objectMapDTOList.stream()
                .filter(dto -> !StringUtils.isEmpty(dto.getKey()))
                .map(ObjectMapDTO::getKey)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(keys)) {
            return;
        }

        // 获取第一个对象的modelId作为查询条件
        String modelId = objectMapDTOList.get(0).getModelId();

        // 查询是否已存在相同key的对象映射
        QueryObjectMapByKeysArg arg = new QueryObjectMapByKeysArg();
        arg.setTenantId(tenantId);
        arg.setModelId(modelId);
        arg.setKeys(keys);

        QueryObjectMapByKeysResult result = objectMapService.queryObjectMapByKeys(arg);

        if (result != null && !CollectionUtils.isEmpty(result.getObjectMaps())) {
            // 找出冲突的key
            List<String> conflictKeys = result.getObjectMaps().stream()
                    .map(ObjectMapDto::getKey)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(conflictKeys)) {
                // 抛出异常，指出冲突的key
                String errorMsg = String.join(", ", conflictKeys);
                log.info("添加对象映射失败，key冲突: {}", errorMsg);
                throw new FmcgException(ErrorCode.OBJECT_MAP_KEY_CONFLICT.getMessage(), ErrorCode.OBJECT_MAP_KEY_CONFLICT.getCode(), errorMsg);
            }
        }
    }

    @Override
    public List<ObjectMapDTO> queryObjectMapByIds(Integer tenantId, List<String> objectMapIds) {
        if (tenantId == null || CollectionUtils.isEmpty(objectMapIds)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            List<ObjectMapDto> dbObjectDto = queryObjectMapDtoByIds(tenantId, objectMapIds);

            return dbObjectDto.stream()
                    .map(this::transform)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据ID查询对象映射失败, tenantId={}, objectMapIds={}", tenantId, objectMapIds, e);
            throw new FmcgException(ErrorCode.OBJECT_MAP_QUERY_BUSINESS_ERROR);
        }
    }

    @Override
    public AIDetectRuleDTO addAIDetectRule(Integer tenantId, AIDetectRuleDTO aiDetectRuleDTO) {
        if (tenantId == null || aiDetectRuleDTO == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            AIDetectRuleDto ruleDto = new AIDetectRuleDto();
            BeanUtils.copyProperties(aiDetectRuleDTO, ruleDto);
            ruleDto.setTenantId(tenantId);
            ruleDto.setCreateTime(System.currentTimeMillis());
            ruleDto.setLastModifyTime(System.currentTimeMillis());
            SaveAIDetectRuleArg arg = new SaveAIDetectRuleArg();
            arg.setTenantId(tenantId);
            arg.setRule(ruleDto);

            SaveAIDetectRuleResult result = aiDetectRuleService.saveRule(arg);
            if (result != null && !Boolean.TRUE.equals(result.getSuccess())) {
                log.info("添加AI检测规则失败。arg:{}", arg);
                throw new FmcgException(ErrorCode.AI_DETECT_RULE_ADD_BUSINESS_ERROR);
            }
            if (result == null || !Boolean.TRUE.equals(result.getSuccess()) || result.getRule() == null) {
                return null;
            }

            AIDetectRuleDTO resultDTO = new AIDetectRuleDTO();
            BeanUtils.copyProperties(result.getRule(), resultDTO);
            return resultDTO;
        } catch (AiProviderException aiProviderException) {
            log.info("添加AI检测规则失败。arg:{}", aiDetectRuleDTO);
            throw new FmcgException(aiProviderException.getMessage(), -1);
        } catch (Exception e) {
            log.error("添加AI检测规则失败, tenantId={}, rule={}", tenantId, aiDetectRuleDTO, e);
            throw new FmcgException(ErrorCode.AI_DETECT_RULE_ADD_BUSINESS_ERROR);
        }
    }

    @Override
    public AIDetectRuleDTO updateAIDetectRule(Integer tenantId, Integer userId, AIDetectRuleDTO aiDetectRuleDTO) {
        if (tenantId == null || userId == null || aiDetectRuleDTO == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            AIDetectRuleDto ruleDto = new AIDetectRuleDto();
            BeanUtils.copyProperties(aiDetectRuleDTO, ruleDto);
            if (aiDetectRuleDTO.getCreator() != null) {
                ruleDto.setCreateTime(aiDetectRuleDTO.getCreateTime());
                ruleDto.setCreator(aiDetectRuleDTO.getCreator().getId());
            }
            ruleDto.setTenantId(tenantId);
            ruleDto.setLastModifier(userId);
            ruleDto.setLastModifyTime(System.currentTimeMillis());
            UpdateAIDetectRuleArg arg = new UpdateAIDetectRuleArg();
            arg.setTenantId(tenantId);
            arg.setRule(ruleDto);

            UpdateAIDetectRuleResult result = aiDetectRuleService.updateRule(arg);
            if (result != null && !Boolean.TRUE.equals(result.getSuccess())) {
                log.info("更新AI检测规则失败。arg:{}", arg);
                throw new FmcgException(ErrorCode.AI_DETECT_RULE_UPDATE_BUSINESS_ERROR);
            }

            if (result == null || !Boolean.TRUE.equals(result.getSuccess()) || result.getRule() == null) {
                return null;
            }

            AIDetectRuleDTO resultDTO = new AIDetectRuleDTO();
            BeanUtils.copyProperties(result.getRule(), resultDTO);
            return resultDTO;
        } catch (Exception e) {
            log.error("更新AI检测规则失败, tenantId={}, userId={}, rule={}", tenantId, userId, aiDetectRuleDTO, e);
            throw new FmcgException(ErrorCode.AI_DETECT_RULE_UPDATE_BUSINESS_ERROR);
        }
    }

    @Override
    public List<AIDetectRuleDTO> queryAIDetectRuleListByModelId(Integer tenantId, String modelId) {
        if (tenantId == null || StringUtils.isEmpty(modelId)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            GetAIDetectRuleListArg arg = new GetAIDetectRuleListArg();
            arg.setTenantId(tenantId);
            arg.setModelId(modelId);
            GetAIDetectRuleListResult result = aiDetectRuleService.getRuleList(arg);
            if (result != null && !Boolean.TRUE.equals(result.getSuccess())) {
                log.info("查询ai规则失败。arg:{}", arg);
                throw new FmcgException(ErrorCode.AI_DETECT_RULE_LIST_QUERY_BUSINESS_ERROR);
            }
            if (result == null || CollectionUtils.isEmpty(result.getRuleList())) {
                return new ArrayList<>();
            }


            // 获取用户信息映射
            Map<Integer, UserInfo> userInfoMap = getUserInfoByRules(tenantId, result.getRuleList());

            // 转换规则并设置用户信息
            return result.getRuleList().stream()
                    .map(rule -> transform(rule, userInfoMap))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询AI检测规则列表失败, tenantId={}, modelId={}", tenantId, modelId, e);
            throw new FmcgException(ErrorCode.AI_DETECT_RULE_LIST_QUERY_BUSINESS_ERROR);
        }
    }

    private Map<Integer, UserInfo> getUserInfoByRules(Integer tenantId, List<AIDetectRuleDto> rules) {
        if (tenantId == null || CollectionUtils.isEmpty(rules)) {
            return Collections.emptyMap();
        }
        // 获取规则中的创建者和修改者ID
        Set<Integer> employeeIds = rules.stream()
                .filter(rule -> rule != null)
                .flatMap(rule -> {
                    Set<Integer> ids = new HashSet<>();
                    if (rule.getCreator() != null) {
                        ids.add(rule.getCreator());
                    }
                    if (rule.getLastModifier() != null) {
                        ids.add(rule.getLastModifier());
                    }
                    return ids.stream();
                })
                .collect(Collectors.toSet());

        // 获取用户信息映射
        return getUserInfoMap(tenantId, employeeIds);


    }

    @Override
    public void deleteAIDetectRule(Integer tenantId, Integer userId, String ruleId) {
        if (tenantId == null || userId == null || StringUtils.isEmpty(ruleId)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            DeleteAIDetectRuleArg arg = new DeleteAIDetectRuleArg();
            arg.setTenantId(tenantId);
            arg.setUserId(userId);
            arg.setRuleId(ruleId);
            aiDetectRuleService.deleteRule(arg);
        } catch (Exception e) {
            log.error("删除AI检测规则失败, tenantId={}, userId={}, ruleId={}", tenantId, userId, ruleId, e);
            throw new FmcgException(ErrorCode.AI_DETECT_RULE_DELETE_BUSINESS_ERROR);
        }
    }

    @Override
    public AIDetectRuleDTO getAIDetectRuleById(Integer tenantId, String ruleId) {
        if (tenantId == null || StringUtils.isEmpty(ruleId)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            GetAIDetectRuleArg arg = new GetAIDetectRuleArg();
            arg.setTenantId(tenantId);
            arg.setRuleId(ruleId);

            GetAIDetectRuleResult result = aiDetectRuleService.getRuleById(arg);
            if (result != null && !Boolean.TRUE.equals(result.getSuccess())) {
                log.info("查询AI检测规则失败。arg:{}", arg);
                throw new FmcgException(ErrorCode.AI_DETECT_RULE_QUERY_BUSINESS_ERROR);
            }

            if (result == null || !Boolean.TRUE.equals(result.getSuccess()) || result.getRule() == null) {
                return null;
            }

            // 获取规则中的创建者和修改者ID
            Map<Integer, UserInfo> userInfoMap = getUserInfoByRules(tenantId, Lists.newArrayList(result.getRule()));

            return transform(result.getRule(), userInfoMap);
        } catch (Exception e) {
            log.error("查询AI检测规则失败, tenantId={}, ruleId={}", tenantId, ruleId, e);
            throw new FmcgException(ErrorCode.AI_DETECT_RULE_QUERY_BUSINESS_ERROR);
        }
    }

    @Override
    public List<AIDetectRuleDTO> queryAIDetectRulesByIds(Integer tenantId, List<String> ruleIds) {
        if (tenantId == null || CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }

        try {
            QueryRulesByIdsArg arg = new QueryRulesByIdsArg();
            arg.setTenantId(tenantId);
            arg.setRuleIds(ruleIds);
            QueryRulesByIdsResult result = aiDetectRuleService.queryRulesByIds(arg);

            if (result != null && !Boolean.TRUE.equals(result.getSuccess())) {
                log.info("批量查询AI检测规则失败。arg:{}", arg);
                throw new FmcgException(ErrorCode.AI_DETECT_RULE_BATCH_QUERY_BUSINESS_ERROR);
            }

            if (result == null || CollectionUtils.isEmpty(result.getRules())) {
                return new ArrayList<>();
            }
            Map<Integer, UserInfo> userInfoMap = getUserInfoByRules(tenantId, result.getRules());
            return result.getRules().stream()
                    .map(rule -> transform(rule, userInfoMap))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量查询AI检测规则失败, tenantId={}, ruleIds={}", tenantId, ruleIds, e);
            throw new FmcgException(ErrorCode.AI_DETECT_RULE_BATCH_QUERY_BUSINESS_ERROR);
        }
    }

}
