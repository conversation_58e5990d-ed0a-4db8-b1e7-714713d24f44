package com.facishare.fmcg.adapter.util;

import com.facishare.ai.detector.api.dto.ApplicationDto;
import com.facishare.fmcg.api.dto.ai.model.ModelDTO;
import com.facishare.fmcg.api.dto.ai.model.TokenInfoDTO;
import com.fs.fmcg.sdk.ai.plat.TokenPlatFormEnum;

import java.util.Map;

/**
 * 数据转换工具类
 */
public class ConvertUtil {

    /**
     * 将 ApplicationDto 转换为 TokenInfoDTO
     *
     * @return TokenInfoDTO对象
     */
    public static TokenInfoDTO convertToTokenInfoDTO(ModelDTO modelDTO) {

        TokenInfoDTO tokenInfoDTO = new TokenInfoDTO();
        switch (modelDTO.getModelManufacturer()) {
            case "baidu":
                tokenInfoDTO.setType(TokenPlatFormEnum.BAIDU.code());
                break;
            case "tu_jiang":
                tokenInfoDTO.setType(TokenPlatFormEnum.TU_JIANG.code());
                break;
            case "sense_time":
                tokenInfoDTO.setType(TokenPlatFormEnum.SENSE_TIME.code());
                break;
            case "huawei":
                tokenInfoDTO.setType(TokenPlatFormEnum.HUAWEI.code());
                break;
            case "rio":
                tokenInfoDTO.setType(TokenPlatFormEnum.RIO.code());
                break;
            case "yqsl":
                tokenInfoDTO.setType(TokenPlatFormEnum.YQSL.code());
                break;
            case "meng_niu":
            case "default":
            case "fs":
                tokenInfoDTO.setType(TokenPlatFormEnum.DEFAULT.code());
                break;
            default:
                break;
        }

        tokenInfoDTO.setIdentityKey(modelDTO.getToken_identityKey());
        tokenInfoDTO.setAk(modelDTO.getToken_ak());
        tokenInfoDTO.setSk(modelDTO.getToken_sk());
        tokenInfoDTO.setHost(modelDTO.getToken_host());
        tokenInfoDTO.setAppKey(modelDTO.getToken_appKey());
        tokenInfoDTO.setSecretKey(modelDTO.getToken_secretKey());
        tokenInfoDTO.setUserName(modelDTO.getToken_userName());
        tokenInfoDTO.setPassword(modelDTO.getToken_password());
        tokenInfoDTO.setDomainName(modelDTO.getToken_domainName());
        tokenInfoDTO.setProjectName(modelDTO.getToken_projectName());
        tokenInfoDTO.setToken(modelDTO.getToken_token());
        tokenInfoDTO.setSecretId(modelDTO.getToken_secretId());
        tokenInfoDTO.setAccount(modelDTO.getToken_account());
        tokenInfoDTO.setCompCode(modelDTO.getToken_compCode());
        tokenInfoDTO.setUrl(modelDTO.getToken_url());
        tokenInfoDTO.setAppId(modelDTO.getToken_appId());
        tokenInfoDTO.setKey(modelDTO.getToken_key());
        tokenInfoDTO.setGrantType(modelDTO.getToken_grantType());
        tokenInfoDTO.setSecret(modelDTO.getToken_secret());
        modelDTO.setTokenInfo(tokenInfoDTO);
        return tokenInfoDTO;
    }


    public static void fillModelDTOByApplicationDto(ModelDTO modelDTO, ApplicationDto applicationDto, boolean isMask) {
        TokenInfoDTO tokenInfoDTO = new TokenInfoDTO();
        tokenInfoDTO.setType(applicationDto.getPlatForm());
        Map<String, Object> params = applicationDto.getParams();
        if (params != null) {
            tokenInfoDTO.setIdentityKey(applicationDto.getIdentityKey());
            if (isMask) {
                tokenInfoDTO.setAk(maskString((String) params.get("ak")));
                tokenInfoDTO.setSk(maskString((String) params.get("sk")));
                tokenInfoDTO.setHost(maskString((String) params.get("host")));
                tokenInfoDTO.setAppKey(maskString((String) params.get("appKey")));
                tokenInfoDTO.setSecretKey(maskString((String) params.get("secretKey")));
                tokenInfoDTO.setUserName(maskString((String) params.get("userName")));
                tokenInfoDTO.setPassword(maskString((String) params.get("password")));
                tokenInfoDTO.setDomainName(maskString((String) params.get("domainName")));
                tokenInfoDTO.setProjectName(maskString((String) params.get("projectName")));
                tokenInfoDTO.setToken(maskString((String) params.get("token")));
                tokenInfoDTO.setSecretId(maskString((String) params.get("secretId")));
                tokenInfoDTO.setAccount(maskString((String) params.get("account")));
                tokenInfoDTO.setCompCode(maskString((String) params.get("compCode")));
                tokenInfoDTO.setUrl(maskString((String) params.get("url")));
                tokenInfoDTO.setAppId(maskString((String) params.get("appId")));
                tokenInfoDTO.setKey(maskString((String) params.get("key")));
                tokenInfoDTO.setGrantType(maskString((String) params.get("grantType")));
                tokenInfoDTO.setSecret(maskString((String) params.get("secret")));
            } else {
                tokenInfoDTO.setAk((String) params.get("ak"));
                tokenInfoDTO.setSk((String) params.get("sk"));
                tokenInfoDTO.setHost((String) params.get("host"));
                tokenInfoDTO.setAppKey((String) params.get("appKey"));
                tokenInfoDTO.setSecretKey((String) params.get("secretKey"));
                tokenInfoDTO.setUserName((String) params.get("userName"));
                tokenInfoDTO.setPassword((String) params.get("password"));
                tokenInfoDTO.setDomainName((String) params.get("domainName"));
                tokenInfoDTO.setProjectName((String) params.get("projectName"));
                tokenInfoDTO.setToken((String) params.get("token"));
                tokenInfoDTO.setSecretId((String) params.get("secretId"));
                tokenInfoDTO.setAccount((String) params.get("account"));
                tokenInfoDTO.setCompCode((String) params.get("compCode"));
                tokenInfoDTO.setUrl((String) params.get("url"));
                tokenInfoDTO.setAppId((String) params.get("appId"));
                tokenInfoDTO.setKey((String) params.get("key"));
                tokenInfoDTO.setGrantType((String) params.get("grantType"));
                tokenInfoDTO.setSecret((String) params.get("secret"));
            }
        }
        modelDTO.setTokenInfo(tokenInfoDTO);
        fillModelDTOByTokenInfo(modelDTO, tokenInfoDTO);
    }

    /**
     * 对字符串进行掩码处理
     * 保留首尾字符，中间用*号代替
     *
     * @param str 需要掩码的字符串
     * @return 掩码后的字符串
     */
    private static String maskString(String str) {
        if (str == null || str.length() <= 2) {
            return str;
        }
        
        int len = str.length();
        int maskLen = len - 2;  // 掩码的长度（去除首尾字符）
        StringBuilder masked = new StringBuilder();
        masked.append(str.charAt(0));  // 保留第一个字符
        masked.append("******");  // 中间用*号代替
        masked.append(str.charAt(len - 1));  // 保留最后一个字符
        
        return masked.toString();
    }

    /**
     * 将TokenInfoDTO的内容回填到ModelDTO中
     *
     * @param modelDTO     需要被填充的ModelDTO对象
     * @param tokenInfoDTO 包含token信息的TokenInfoDTO对象
     */
    public static void fillModelDTOByTokenInfo(ModelDTO modelDTO, TokenInfoDTO tokenInfoDTO) {
        if (tokenInfoDTO == null || modelDTO == null) {
            return;
        }

        modelDTO.setToken_identityKey(tokenInfoDTO.getIdentityKey());
        modelDTO.setToken_type(tokenInfoDTO.getType());
        modelDTO.setToken_ak(tokenInfoDTO.getAk());
        modelDTO.setToken_sk(tokenInfoDTO.getSk());
        modelDTO.setToken_host(tokenInfoDTO.getHost());
        modelDTO.setToken_appKey(tokenInfoDTO.getAppKey());
        modelDTO.setToken_secretKey(tokenInfoDTO.getSecretKey());
        modelDTO.setToken_userName(tokenInfoDTO.getUserName());
        modelDTO.setToken_password(tokenInfoDTO.getPassword());
        modelDTO.setToken_domainName(tokenInfoDTO.getDomainName());
        modelDTO.setToken_projectName(tokenInfoDTO.getProjectName());
        modelDTO.setToken_token(tokenInfoDTO.getToken());
        modelDTO.setToken_secretId(tokenInfoDTO.getSecretId());
        modelDTO.setToken_account(tokenInfoDTO.getAccount());
        modelDTO.setToken_compCode(tokenInfoDTO.getCompCode());
        modelDTO.setToken_url(tokenInfoDTO.getUrl());
        modelDTO.setToken_appId(tokenInfoDTO.getAppId());
        modelDTO.setToken_key(tokenInfoDTO.getKey());
        modelDTO.setToken_grantType(tokenInfoDTO.getGrantType());
        modelDTO.setToken_secret(tokenInfoDTO.getSecret());
    }
}
