{"groupList": [{"label": "商品陈列识别", "value": "goodsDisplay", "i18nKey": "fmcg.ai.rule.groupList.label.goodsDisplay"}, {"label": "商品陈列形式识别", "value": "displayForm", "i18nKey": "fmcg.ai.rule.groupList.label.displayForm"}, {"label": "物料识别", "value": "posm", "i18nKey": "fmcg.ai.rule.groupList.label.posm"}, {"label": "门头识别", "value": "storefront", "i18nKey": "fmcg.ai.rule.groupList.label.storefront"}], "detectCapabilityList": [{"label": "商品陈列识别", "value": "isOpenProductRowNumber", "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.isOpenProductRowNumber"}, {"label": "商品陈列组数", "value": "isOpenGroupNumber", "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.isOpenGroupNumber"}, {"label": "货架层数识别", "value": "isOpenLayerNumber", "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.isOpenLayerNumber"}, {"label": "商品单位识别", "value": "openSkuUnit", "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.openSkuUnit"}, {"label": "商品价格识别", "value": "isOpenPrices", "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.isOpenPrices"}, {"label": "商品陈列场景", "value": "isOpenSceneDetect", "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.isOpenSceneDetect"}, {"label": "商品陈列形式", "value": "isOpenDisplayForm", "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.isOpenDisplayForm"}, {"label": "物料识别", "value": "isPOSMDetect", "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.isPOSMDetect"}, {"label": "门头照识别", "value": "isOpenStorefrontDetect", "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.isOpenStorefrontDetect"}], "fieldsMap": {"aiPath": {"type": "mapping", "fieldApiName": "a<PERSON><PERSON><PERSON>", "label": "AI图片", "supportCalculateTypes": [], "supportFieldType": ["image"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.aiPath"}, "productName": {"type": "mapping", "fieldApiName": "productName", "label": "产品名称", "supportCalculateTypes": [], "supportFieldType": ["object_reference"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.productName"}, "aiRowNumber": {"type": "mapping", "fieldApiName": "aiRowNumber", "label": "商品排面数", "supportCalculateTypes": [0, 1, 3, 5], "supportFieldType": ["number"], "enableManualInput": true, "enableCalculateType": true, "i18nKey": "fmcg.ai.rule.fieldsMap.label.aiRowNumber"}, "aiGroupNumber": {"type": "mapping", "fieldApiName": "aiGroupNumber", "label": "商品组数", "supportCalculateTypes": [0, 1, 2, 3], "supportFieldType": ["number"], "enableManualInput": true, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.aiGroupNumber"}, "aiLayerNumber": {"type": "mapping", "fieldApiName": "aiLayerNumber", "label": "层数", "supportCalculateTypes": [], "supportFieldType": ["number"], "enableManualInput": true, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.aiLayerNumber"}, "aiScene": {"type": "mapping", "fieldApiName": "aiScene", "label": "场景", "supportCalculateTypes": [], "supportFieldType": ["select_one", "object_reference"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.aiSceneField"}, "aiUnitField": {"type": "mapping", "fieldApiName": "aiUnitField", "label": "单位", "supportCalculateTypes": [], "supportFieldType": ["object_reference"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.aiUnitField"}, "aiPrices": {"type": "mapping", "fieldApiName": "aiPrices", "label": "产品价格", "supportCalculateTypes": [], "supportFieldType": ["number", "currency", "text"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.aiPrices"}, "productScene": {"type": "mapping", "fieldApiName": "productScene", "label": "陈列形式", "supportCalculateTypes": [], "supportFieldType": ["select_one", "object_reference"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.productScene"}, "displaySceneField": {"type": "mapping", "fieldApiName": "displaySceneField", "label": "陈列形式", "supportCalculateTypes": [], "supportFieldType": ["select_one", "object_reference"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.displaySceneField"}, "displayTotalLayerNumber": {"type": "mapping", "fieldApiName": "displayTotalLayerNumber", "label": "陈列形式层数", "supportCalculateTypes": [4, 5], "supportFieldType": ["number"], "enableManualInput": true, "enableCalculateType": true, "i18nKey": "fmcg.ai.rule.fieldsMap.label.displayTotalLayerNumber"}, "displayCutBoxGroupNumber": {"type": "mapping", "fieldApiName": "displayCutBoxGroupNumber", "label": "割箱组数", "supportCalculateTypes": [4, 5], "supportFieldType": ["number"], "enableManualInput": false, "enableCalculateType": true, "i18nKey": "fmcg.ai.rule.fieldsMap.label.displayCutBoxNumber"}, "displayMaxVisibleNumber": {"type": "mapping", "fieldApiName": "displayMaxVisibleNumber", "label": "地堆最大可视数", "supportCalculateTypes": [4, 5], "supportFieldType": ["number"], "enableManualInput": false, "enableCalculateType": true, "i18nKey": "fmcg.ai.rule.fieldsMap.label.displayMaxVisibleNumber"}, "displayAiPath": {"type": "mapping", "fieldApiName": "displayAiPath", "label": "陈列形式照片", "supportCalculateTypes": [], "supportFieldType": ["image"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.displayAiPath"}, "displaySinglePhotoMaxRowNumber": {"type": "mapping", "fieldApiName": "displaySinglePhotoMaxRowNumber", "label": "单张照片最大排面", "supportCalculateTypes": [], "supportFieldType": ["number"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.displaySinglePhotoMaxRowNumber"}, "posmDisplayScene": {"type": "mapping", "fieldApiName": "posmDisplayScene", "label": "陈列形式", "supportCalculateTypes": [], "supportFieldType": ["select_one", "object_reference"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.posmDisplayScene"}, "posmName": {"type": "mapping", "fieldApiName": "posmName", "label": "物料名称", "supportCalculateTypes": [], "supportFieldType": ["object_reference"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.posmName"}, "posmNumber": {"type": "mapping", "fieldApiName": "posmNumber", "label": "物料数量", "supportCalculateTypes": [4, 5], "supportFieldType": ["number"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.posmNumber"}, "posmPrices": {"type": "mapping", "fieldApiName": "posmPrices", "label": "POSM价格", "supportCalculateTypes": [], "supportFieldType": ["text"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.posmPrices"}, "posmRelatedSku": {"type": "mapping", "fieldApiName": "posmRelatedSku", "label": "附近的sku", "supportCalculateTypes": [], "supportFieldType": ["object_reference_many"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.posmRelatedSku"}, "promptTemplate": {"type": "select_one", "fieldApiName": "promptTemplate", "label": "提示词模版", "supportCalculateTypes": [], "supportFieldType": [], "options": [], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.promptTemplate"}, "storeName": {"type": "mapping", "fieldApiName": "storeName", "label": "门店名称", "supportCalculateTypes": [], "supportFieldType": ["text"], "enableManualInput": true, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.storeName"}, "isStorefront": {"type": "mapping", "fieldApiName": "isStorefront", "label": "是否门头照", "supportCalculateTypes": [], "supportFieldType": ["true_or_false"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.isStorefront"}, "isMatchStorefrontName": {"type": "mapping", "fieldApiName": "isMatchStorefrontName", "label": "是否与门店名称匹配", "supportCalculateTypes": [], "supportFieldType": ["true_or_false"], "enableManualInput": false, "enableCalculateType": false, "i18nKey": "fmcg.ai.rule.fieldsMap.label.isMatchStorefrontName"}}, "groupFieldRelationList": [{"group": "goodsDisplay", "fields": ["productScene", "productName", "aiRowNumber", "aiGroupNumber", "aiUnitField", "aiPrices"], "order": 1}, {"group": "displayForm", "fields": ["displaySceneField", "displayAiPath", "displayTotalLayerNumber", "displayMaxVisibleNumber", "displayCutBoxGroupNumber", "displaySinglePhotoMaxRowNumber"], "order": 2}, {"group": "posm", "fields": ["posmDisplayScene", "posmName", "posmNumber", "posmPrices", "posmRelatedSku"], "order": 3}, {"group": "storefront", "fields": ["storeName", "isStorefront", "isMatchStorefrontName"], "order": 4}], "detectCapabilityFieldRelationMap": {"isOpenProductRowNumber": ["productName", "aiRowNumber"], "isOpenGroupNumber": ["aiGroupNumber"], "isOpenLayerNumber": ["aiLayerNumber"], "openSkuUnit": ["aiUnitField"], "isOpenPrices": ["aiPrices", "posmPrices"], "isOpenSceneDetect": ["productScene"], "isOpenDisplayForm": ["posmDisplayScene", "productScene", "displaySceneField", "displayAiPath", "displayTotalLayerNumber", "displayMaxVisibleNumber", "displayCutBoxGroupNumber", "displaySinglePhotoMaxRowNumber"], "isPOSMDetect": ["posmName", "posmNumber", "posmRelatedSku"], "isOpenStorefrontDetect": []}, "calculateTypesMap": {"0": {"label": "所有照片排面数求和", "value": 0, "i18nKey": "fmcg.ai.rule.calculateTypesMap.label.0"}, "1": {"label": "所有照片排面数求最大值", "value": 1, "i18nKey": "fmcg.ai.rule.calculateTypesMap.label.1"}, "2": {"label": "统计陈列形式下单张图片排面汇总后，多张对比取最大值（不做过滤）", "value": 2, "i18nKey": "fmcg.ai.rule.calculateTypesMap.label.2"}, "3": {"label": "陈列形式下单张图片排面汇总后，多张对比取最大值（做错误场景过滤）", "value": 3, "i18nKey": "fmcg.ai.rule.calculateTypesMap.label.3"}, "4": {"label": "统计陈列形式多张照片取单张最大值", "value": 4, "i18nKey": "fmcg.ai.rule.calculateTypesMap.label.4"}, "5": {"label": "陈列形式多张照片取单张最大值（做错误场景图片过滤）", "value": 5, "i18nKey": "fmcg.ai.rule.calculateTypesMap.label.5"}}, "fieldTypeMap": {"text": {"label": "单行文本", "value": "text", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.text"}, "long_text": {"label": "多行文本", "value": "long_text", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.long_text"}, "big_text": {"label": "长文本", "value": "big_text", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.big_text"}, "select_one": {"label": "单选", "value": "select_one", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.select_one"}, "select_many": {"label": "多选", "value": "select_many", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.select_many"}, "number": {"label": "数字", "value": "number", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.number"}, "currency": {"label": "金额", "value": "currency", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.currency"}, "date": {"label": "日期", "value": "date", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.date"}, "time": {"label": "时间", "value": "time", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.time"}, "date_time": {"label": "日期时间", "value": "date_time", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.date_time"}, "phone_number": {"label": "手机", "value": "phone_number", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.phone_number"}, "image": {"label": "图片", "value": "image", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.image"}, "true_or_false": {"label": "布尔值", "value": "true_or_false", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.true_or_false"}, "object_reference": {"label": "查找关联", "value": "object_reference", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.object_reference"}, "object_reference_many": {"label": "查找关联(多选)", "value": "object_reference_many", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.object_reference_many"}, "date_time_range": {"label": "日期范围", "value": "date_time_range", "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.date_time_range"}}}