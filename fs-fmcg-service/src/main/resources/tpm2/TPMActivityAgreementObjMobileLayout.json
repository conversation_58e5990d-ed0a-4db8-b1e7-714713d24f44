{"components": [{"buttons": [], "api_name": "table_component", "ref_object_api_name": "TPMActivityAgreementObj", "include_fields": [{"api_name": "name", "render_type": "text", "field_name": "name", "is_show_label": true}, {"api_name": "store_id", "render_type": "object_reference", "field_name": "store_id"}, {"api_name": "dealer_id", "render_type": "object_reference", "field_name": "dealer_id"}, {"api_name": "begin_date", "render_type": "date", "field_name": "begin_date", "is_show_label": true}, {"api_name": "end_date", "render_type": "date", "field_name": "end_date", "is_show_label": true}, {"api_name": "agreement_status", "render_type": "select_one", "field_name": "agreement_status", "is_show_label": true}, {"api_name": "total", "render_type": "count", "field_name": "total"}, {"api_name": "owner", "render_type": "employee", "field_name": "owner"}], "type": "table"}], "ref_object_api_name": "TPMActivityAgreementObj", "layout_type": "list", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "buttons": [], "package": "CRM", "display_name": "移动端默认列表页", "is_default": false, "version": 1, "agent_type": "agent_type_mobile", "api_name": "layout_TPMActivityAgreementObj_mobile", "is_show_fieldname": true}