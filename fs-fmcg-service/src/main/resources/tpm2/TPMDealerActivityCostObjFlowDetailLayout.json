{"components": [{"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": true, "is_required": true, "render_type": "currency", "field_name": "audited_amount"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "goods_pay_number"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "currency", "field_name": "confirmed_amount"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "cost_cashing_quantity"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "cashing_fund_account_id"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_one", "field_name": "cash_usage"}, {"is_readonly": false, "is_required": false, "is_tiled": true, "render_type": "select_one", "field_name": "goods_pay_usage"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "effective_period"}], "api_name": "group_yHXpN__c", "tab_index": "ltr", "column": 2, "header": "本次核销信息", "collapse": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "isSticky": false, "grayLimit": 1}, {"buttons": [], "api_name": "TPMDealerActivityCashingProductObj_md_group_component", "related_list_name": "target_related_list_TPMDealerActivityCashingProductObj_TPMDealerActivityCostObj__c", "button_info": [], "ref_object_api_name": "TPMDealerActivityCashingProductObj", "child_components": [], "header": "申请核销兑付产品", "nameI18nKey": "TPMDealerActivityCashingProductObj.field.dealer_activity_cost_id.reference_label", "flow_layout_api_name": "layout_flow_default_TPMDealerActivityCashingProductObj__c", "type": "multi_table", "isSticky": false, "field_api_name": "dealer_activity_cost_id"}], "ref_object_api_name": "TPMDealerActivityCostObj", "layout_type": "detail", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "layout_structure": {"layout": [{"components": [[]], "columns": [{"width": "100%"}]}, {"components": [["form_component", "TPMDealerActivityCashingProductObj_md_group_component"]], "columns": [{"width": "auto"}]}], "flow_ui_version": 820, "layout_structure_type": 1}, "buttons": [], "package": "CRM", "display_name": "费用核销流程布局", "is_default": false, "version": 1, "api_name": "layout_flow_default_TPMDealerActivityCostObj__c", "namespace": "flow", "layout_description": ""}