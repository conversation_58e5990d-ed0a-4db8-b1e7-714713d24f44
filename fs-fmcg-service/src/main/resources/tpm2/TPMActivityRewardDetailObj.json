{"store_table_name": "fmcg_tpm_activity_reward_detail", "description": "", "enabled_change_order": false, "index_version": 1, "is_deleted": false, "define_type": "package", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "活动奖励发放明细", "is_open_display_name": false, "icon_index": 11, "api_name": "TPMActivityRewardDetailObj", "icon_path": "", "short_name": "nhu", "fields": {"tenant_id": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "status": "new"}, "trigger_event": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "", "label": "触发事件", "type": "select_one", "default_to_zero": false, "is_required": false, "api_name": "trigger_event", "options": [{"label": "签收入库", "value": "SIGN_CODE_SIGN"}, {"label": "销售出库", "value": "SALES_OUT_OF_WAREHOUSE"}, {"label": "退货入库", "value": "RETURN_BACK"}, {"label": "换货入库", "value": "EXCHANGE_RETURN_IN"}, {"label": "换货出库", "value": "EXCHANGE_RETURN_OUT"}, {"label": "门店签收", "value": "STORE_SIGN"}, {"label": "消费者领红包", "value": "PRESET_CONSUMER_RED_PACKET"}, {"label": "其他入库", "value": "OTHER_RETURN_IN"}, {"label": "消费者扫码", "value": "CONSUMER_SCAN_CODE"}, {"label": "门店销量登记", "value": "STORE_SALES"}, {"label": "门店库存盘点", "value": "STORE_STOCK_CHECK"}, {"label": "门店库存核销", "value": "STORE_CHECK_WRITE_OFFS"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "reward_value": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 12, "default_value": "", "label": "奖励值", "api_name": "reward_value", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "reward_type": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "redPacket", "label": "奖励方式", "type": "select_one", "default_to_zero": false, "is_required": false, "api_name": "reward_type", "options": [{"label": "红包", "value": "redPacket"}, {"label": "减价支付", "value": "discount"}, {"label": "实物", "value": "physicalItem"}, {"label": "积分", "value": "points"}, {"label": "无", "value": "none"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "new"}, "reward_part": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "奖励角色", "api_name": "reward_part", "is_show_mask": false, "help_text": "", "status": "new"}, "reward_part_code": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "奖励角色编码", "api_name": "reward_part_code", "is_show_mask": false, "help_text": "", "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "status": "released"}, "reward_time": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "date_time", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "not_use_multitime_zone": false, "default_value": "", "label": "奖励时间", "time_zone": "GMT+8", "api_name": "reward_time", "date_format": "yyyy-MM-dd HH:mm", "help_text": "", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "product_id": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "产品名称", "target_api_name": "ProductObj", "target_related_list_name": "target_related_list_TPMActivityRewardDetailObj_ProductObj__c", "target_related_list_label": "活动奖励发放明细", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "product_id", "help_text": "", "status": "new"}, "activity_id": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "活动名称", "target_api_name": "TPMActivityObj", "target_related_list_name": "target_related_list_TPMActivityRewardDetailObj_TPMActivityObj__c", "target_related_list_label": "活动奖励发放明细", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "activity_id", "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_show_mask": false, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "owner": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "default_value": "", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "0", "label": "锁定状态", "is_need_convert": false, "api_name": "lock_status", "help_text": "", "status": "new"}, "package": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "rewarded_person": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "奖励人", "api_name": "rewarded_person", "is_show_mask": false, "help_text": "", "status": "new"}, "life_status": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "is_need_convert": false, "api_name": "life_status", "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"describe_api_name": "TPMActivityRewardDetailObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "optional_type": "department", "define_type": "package", "is_single": true, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "归属部门", "is_need_convert": false, "api_name": "data_own_department", "help_text": "", "status": "new"}, "name": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "prefix": "ARD-{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "pattern": "", "is_unique": true, "description": "name", "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "postfix": "", "is_single": false, "max_length": 100, "is_index": true, "auto_number_type": "normal", "is_active": true, "is_encrypted": false, "default_value": "ARD-{yyyy}-{mm}-{dd}-000001", "serial_number": 6, "label": "编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "status": "new", "help_text": ""}, "order_by": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "serial_number_id": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "商品条码", "target_api_name": "FMCGSerialNumberObj", "target_related_list_name": "target_related_list_TPMActivityRewardDetailObj_FMCGSerialNumberObj__c", "target_related_list_label": "活动奖励发放明细", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "serial_number_id", "help_text": "", "status": "new"}, "account_id": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "奖励客户", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_TPMActivityRewardDetailObj_AccountObj2__c", "target_related_list_label": "活动奖励发放明细", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "account_id", "help_text": "", "status": "new"}, "_id": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}, "related_object_data_id": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "used_in": "component", "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 256, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "业务单据关联数据", "api_name": "related_object_data_id", "status": "new", "help_text": ""}, "related_object_api_name": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "used_in": "component", "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 256, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "业务单据", "api_name": "related_object_api_name", "status": "new", "help_text": ""}, "related_object": {"describe_api_name": "TPMActivityRewardDetailObj", "is_index": false, "id_field": "related_object_data_id", "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "label": "业务单据信息", "group_type": "what", "type": "group", "is_need_convert": false, "is_required": false, "api_name": "related_object", "define_type": "package", "fields": {"id_field": "related_object_data_id", "api_name_field": "related_object_api_name"}, "is_single": false, "api_name_field": "related_object_api_name", "status": "new", "help_text": ""}, "prize_name": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "enable_clone": true, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "奖品名称", "target_api_name": "TPMActivityPrizesObj", "target_related_list_name": "target_related_list_TPMActivityRewardDetailObj_TPMActivityPrizesObj__c", "target_related_list_label": "活动奖励发放明细", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "prize_name", "help_text": "", "status": "new"}, "reward_person_id": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "奖励人ID", "api_name": "reward_person_id", "is_show_mask": false, "help_text": "", "status": "new"}, "wx_union_id": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "微信UnionId", "api_name": "wx_union_id", "is_show_mask": false, "help_text": "", "status": "new"}, "related_object_data_tenant_id": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "关联对象所在企业", "api_name": "related_object_data_tenant_id", "is_show_mask": false, "help_text": "", "status": "new"}, "business_id": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "业务编号", "api_name": "business_id", "is_show_mask": false, "help_text": "", "status": "new"}, "reward_detail_id": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "活动激励规则明细id", "api_name": "reward_detail_id", "is_show_mask": false, "help_text": "", "status": "new"}, "distribution_instructions": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "发放说明", "api_name": "distribution_instructions", "is_show_mask": false, "help_text": "", "status": "new"}, "status": {"describe_api_name": "TPMActivityRewardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未发放", "value": "0"}, {"font_color": "#1cfd00", "label": "已发放", "value": "1"}, {"font_color": "#ff522a", "label": "发放失败", "value": "2"}, {"label": "无需发放", "value": "3"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "option_type": "field", "default_value": "0", "label": "发放状态", "option_api_name": "", "api_name": "status", "help_text": "", "status": "new"}}}