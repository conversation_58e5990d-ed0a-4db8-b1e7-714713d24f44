{"components": [{"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "type": "user_list"}, {"field_section": [], "buttons": [], "api_name": "sale_log", "related_list_name": "", "header": "跟进动态", "nameI18nKey": "paas.udobj.follow_up_dynamic", "type": "related_record"}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "is_hidden": false, "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record", "order": 1}, {"components": [["form_component"], ["operation_log"]], "buttons": [], "api_name": "container_default_layout_default_TPMActivityAgreementDetailObj__c", "tabs": [{"api_name": "tab_form_component", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "tab_operation_log", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}], "header": "页签容器", "type": "tabs"}, {"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default"}, {"action_type": "default", "api_name": "SaleRecord_button_default"}, {"action_type": "default", "api_name": "Dial_button_default"}, {"action_type": "default", "api_name": "ChangeOwner_button_default"}, {"action_type": "default", "api_name": "StartBPM_button_default"}, {"action_type": "default", "api_name": "Abolish_button_default"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default"}, {"action_type": "default", "api_name": "Lock_button_default"}, {"action_type": "default", "api_name": "Unlock_button_default"}, {"action_type": "default", "api_name": "Clone_button_default"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple"}, {"field_section": [{"render_type": "employee", "field_name": "owner"}, {"render_type": "text", "field_name": "owner_department"}, {"render_type": "date_time", "field_name": "last_modified_time"}, {"render_type": "record_type", "field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "type": "top_info"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "agreement_cost_standard"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "agreement_amount_standard"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "is_report_item_quantity"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "calculate_pattern"}, {"is_readonly": true, "is_required": false, "render_type": "quote", "field_name": "amount_standard_check"}, {"is_readonly": false, "is_required": true, "render_type": "currency", "field_name": "subtotal"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "activity_detail_id"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "activity_item_id"}, {"is_readonly": false, "is_required": true, "render_type": "master_detail", "field_name": "activity_agreement_id"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "is_show": true}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "auto_number", "field_name": "code"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}], "api_name": "group_9q4AJ__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "is_show": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "is_hidden": false, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "order": 4}], "ref_object_api_name": "TPMActivityAgreementDetailObj", "layout_type": "detail", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["top_info", "container_default_layout_default_TPMActivityAgreementDetailObj__c"], ["relevant_team_component", "sale_log"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "buttons": [], "package": "CRM", "display_name": "默认布局", "is_default": true, "version": 2, "api_name": "layout_default_TPMActivityAgreementDetailObj__c", "layout_description": ""}