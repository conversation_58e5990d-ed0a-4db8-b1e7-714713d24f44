{"store_table_name": "fmcg_tpm_activity_agreement_cashing_product", "package": "CRM", "is_active": true, "description": "", "display_name": "协议兑付产品", "is_open_display_name": true, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "TPMActivityAgreementCashingProductObj", "icon_path": "", "define_type": "package", "short_name": "orE", "fields": {"tenant_id": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "status": "new"}, "activity_product_id": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "参与活动兑付产品", "target_api_name": "TPMActivityCashingProductObj", "target_related_list_name": "target_related_list_TPMActivityAgreementCashingProductObj_TPMActivityCashingProductObj__c", "target_related_list_label": "协议兑付产品", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "activity_product_id", "help_text": "", "status": "new"}, "cashing_total_price": {"expression_type": "js", "return_type": "number", "describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_active": true, "expression": "$agreement_price$*$quantity$", "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "单个产品兑付费用", "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "api_name": "cashing_total_price", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "max_length": 256, "status": "new"}, "agreement_price": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "default_is_expression": true, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "$product_id__r.price$", "label": "标准价格", "currency_unit": "￥", "api_name": "agreement_price", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "object_describe_api_name": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "owner": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "status": "new"}, "package": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "quantity": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 12, "default_value": "", "label": "协议兑付数量", "api_name": "quantity", "round_mode": 4, "help_text": "", "status": "new"}, "create_time": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "data_own_department", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "name": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "default_is_expression": false, "prefix": "AACP-{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "pattern": "", "description": "name", "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "postfix": "", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "auto_number_type": "normal", "is_encrypted": false, "serial_number": 8, "default_value": "AACP-{yyyy}-{mm}-{dd}-00000001", "label": "单据编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "help_text": "", "status": "new"}, "activity_agreement_id": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "auto_adapt_places": false, "is_unique": false, "type": "master_detail", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "活动协议", "target_api_name": "TPMActivityAgreementObj", "show_detail_button": false, "target_related_list_name": "target_related_list_TPMActivityAgreementCashingProductObj_TPMActivityAgreementObj__c", "target_related_list_label": "协议兑付产品", "api_name": "activity_agreement_id", "is_create_when_master_create": true, "is_required_when_master_create": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "_id": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}, "product_id": {"describe_api_name": "TPMActivityAgreementCashingProduct", "default_is_expression": true, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": true, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "$activity_product_id__r.product_id$", "label": "产品", "target_api_name": "ProductObj", "target_related_list_name": "target_related_list_TPMActivityAgreementCashingProduct_ProductObj__c", "target_related_list_label": "协议兑付产品", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "product_id", "help_text": "", "status": "new"}, "product_code": {"describe_api_name": "TPMActivityAgreementCashingProduct", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "text", "is_unique": false, "label": "产品编码", "type": "quote", "quote_field": "product_id__r.product_code", "is_required": false, "api_name": "product_code", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "display_name": {"expression_type": "js", "return_type": "text", "describe_api_name": "TPMActivityAgreementCashingProductObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "formula", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "expression": "$activity_product_id__r.display_name$", "is_encrypted": false, "default_value": "", "label": "协议兑付产品名称", "api_name": "display_name", "is_index_field": false, "help_text": "", "status": "new"}, "unit": {"describe_api_name": "TPMActivityAgreementCashingProductObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "单位", "type": "select_one", "is_required": false, "api_name": "unit", "options": [{"label": "个", "value": "1"}, {"label": "块", "value": "2"}, {"label": "只", "value": "3"}, {"label": "把", "value": "4"}, {"label": "枚", "value": "5"}, {"label": "条", "value": "6"}, {"label": "瓶", "value": "7"}, {"label": "盒", "value": "8"}, {"label": "套", "value": "9"}, {"label": "箱", "value": "10"}, {"label": "米", "value": "11"}, {"label": "千克", "value": "12"}, {"label": "吨", "value": "13"}], "define_type": "package", "is_single": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}}, "release_version": "6.4"}