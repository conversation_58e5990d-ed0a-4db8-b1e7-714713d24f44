{"components": [{"field_section": [{"form_fields": [{"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "activity_id"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "activity_agreement_id"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "store_id"}, {"is_readonly": true, "is_required": true, "render_type": "object_reference", "field_name": "dealer_id"}, {"is_readonly": false, "is_required": true, "render_type": "image", "field_name": "proof_images"}, {"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "total"}, {"is_readonly": true, "is_required": false, "render_type": "select_one", "field_name": "audit_status"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "is_show": true}, {"form_fields": [{"is_readonly": false, "is_required": true, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": false, "is_required": true, "render_type": "select_one", "field_name": "life_status"}], "api_name": "group_1k6e6__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "is_show": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "is_hidden": false, "unDeletable": true, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "_id": "form_component", "type": "form", "order": 9}, {"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "is_hidden": false, "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "type": "user_list", "order": 1}, {"field_section": [], "buttons": [], "api_name": "sale_log", "related_list_name": "", "is_hidden": false, "header": "跟进动态", "nameI18nKey": "paas.udobj.follow_up_dynamic", "type": "related_record", "order": 2}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "is_hidden": false, "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record", "order": 3}, {"components": [["form_component"], ["operation_log"]], "buttons": [], "api_name": "tabs_299XZ__c", "tabs": [{"api_name": "form_component_El6Yo__c", "header": "详细信息"}, {"api_name": "operation_log_6ZLlJ__c", "header": "修改记录"}], "is_hidden": false, "header": "页签容器", "type": "tabs", "order": 6}, {"field_section": [], "buttons": [{"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}], "api_name": "head_info", "related_list_name": "", "is_hidden": false, "unDeletable": true, "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "type": "simple", "order": 7}, {"field_section": [{"render_type": "employee", "field_name": "owner"}, {"render_type": "text", "field_name": "owner_department"}, {"render_type": "date_time", "field_name": "last_modified_time"}, {"render_type": "record_type", "field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "is_hidden": false, "header": "摘要信息", "nameI18nKey": "paas.udobj.summary_info", "type": "top_info", "order": 8}, {"buttons": [], "api_name": "TPMActivityProofDetailObj_md_group_component", "limit": 1, "is_hidden": false, "child_components": [], "header": "活动举证项目", "type": "group", "order": 10}, {"relationType": 2, "buttons": [], "api_name": "TPMActivityProofAuditObj_activity_proof_id_related_list", "related_list_name": "target_related_list_TPMActivityProofAuditObj_TPMActivityProofObj__c", "ref_object_api_name": "TPMActivityProofAuditObj", "limit": 1, "is_hidden": false, "header": "活动举证检核", "nameI18nKey": "TPMActivityProofAuditObj.field.activity_proof_id.reference_label", "type": "relatedlist", "order": 12, "field_api_name": "activity_proof_id"}], "ref_object_api_name": "TPMActivityProofObj", "layout_type": "detail", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "buttons": [], "package": "CRM", "display_name": "默认布局", "is_default": true, "version": 2, "api_name": "layout_default_TPMActivityProofObj__c", "layout_description": "", "config": {"edit": 1, "remove": 0}, "top_info": {"field_section": [{"form_fields": [{"render_type": "employee", "field_name": "owner"}, {"render_type": "text", "field_name": "owner_department"}, {"render_type": "date_time", "field_name": "last_modified_time"}, {"render_type": "record_type", "field_name": "record_type"}], "api_name": "detail"}], "buttons": [], "api_name": "top_info", "header": "顶部信息", "type": "simple"}}