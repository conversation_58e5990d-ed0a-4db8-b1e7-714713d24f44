package com.facishare.fmcg.service.controller.inner.ai.detect;

import com.facishare.fmcg.api.annotation.FMCGOuterUserInfo;
import com.facishare.fmcg.api.annotation.FMCGUserInfo;
import com.facishare.fmcg.api.dto.ai.model.AddModel;
import com.facishare.fmcg.api.dto.ai.model.AddToken;
import com.facishare.fmcg.api.dto.ai.model.BatchDeleteObjectMap;
import com.facishare.fmcg.api.dto.ai.model.BatchQueryAIRuleByIds;
import com.facishare.fmcg.api.dto.ai.model.BatchQueryModel;
import com.facishare.fmcg.api.dto.ai.model.BatchSaveOrUpdateObjectMap;
import com.facishare.fmcg.api.dto.ai.model.DeleteDetectRule;
import com.facishare.fmcg.api.dto.ai.model.GetAIModelScenes;
import com.facishare.fmcg.api.dto.ai.model.GetAIModelsByScene;
import com.facishare.fmcg.api.dto.ai.model.GetAIRuleById;
import com.facishare.fmcg.api.dto.ai.model.GetAIRuleDescribe;
import com.facishare.fmcg.api.dto.ai.model.GetDisplayScenesByModelId;
import com.facishare.fmcg.api.dto.ai.model.GetModelById;
import com.facishare.fmcg.api.dto.ai.model.GetModelDescribe;
import com.facishare.fmcg.api.dto.ai.model.ModelSwitch;
import com.facishare.fmcg.api.dto.ai.model.QueryObjectList;
import com.facishare.fmcg.api.dto.ai.model.QueryRuleList;
import com.facishare.fmcg.api.dto.ai.model.SaveOrUpdateDetectRule;
import com.facishare.fmcg.api.dto.ai.model.UpdateModel;
import com.facishare.fmcg.api.model.sys.OuterUserData;
import com.facishare.fmcg.api.model.sys.UserData;
import com.facishare.fmcg.api.service.ai.model.AIModelService;
import com.facishare.fmcg.service.common.ModelConverter;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/ai/model")
public class AIModelInnerController {

    @Resource
    private AIModelService aiModelService;

    @PostMapping("/scenes")
    public GetAIModelScenes.Result getAIModelScenes(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody GetAIModelScenes.Arg arg) {
        return aiModelService.getAIModelScenes(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/getAIModelsByScene")
    public GetAIModelsByScene.Result getAIModelsByScene(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody GetAIModelsByScene.Arg arg) {
        return aiModelService.getAIModelsByScene(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/getModelById")
    public GetModelById.Result getModelById(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody GetModelById.Arg arg) {
        return aiModelService.getModelById(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/modelSwitch")
    public ModelSwitch.Result modelSwitch(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody ModelSwitch.Arg arg) {
        return aiModelService.modelSwitch(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/getModelDescribe")
    public GetModelDescribe.Result getModelDescribe(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody GetModelDescribe.Arg arg) {
        return aiModelService.getModelDescribe(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/addModel")
    public AddModel.Result addModel(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody AddModel.Arg arg) {
        return aiModelService.addModel(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/updateModel")
    public UpdateModel.Result updateModel(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody UpdateModel.Arg arg) {
        return aiModelService.updateModel(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/queryObjectList")
    public QueryObjectList.Result queryObjectList(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody QueryObjectList.Arg arg) {
        return aiModelService.queryObjectList(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/batchDeleteObjectMap")
    public BatchDeleteObjectMap.Result batchDeleteObjectMap(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody BatchDeleteObjectMap.Arg arg) {
        return aiModelService.batchDeleteObjectMap(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/batchSaveOrUpdateObjectMap")
    public BatchSaveOrUpdateObjectMap.Result batchSaveOrUpdateObjectMap(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody BatchSaveOrUpdateObjectMap.Arg arg) {
        return aiModelService.batchSaveOrUpdateObjectMap(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/queryRuleList")
    public QueryRuleList.Result queryRuleList(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody QueryRuleList.Arg arg) {
        return aiModelService.queryRuleList(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/saveOrUpdateDetectRule")
    public SaveOrUpdateDetectRule.Result saveOrUpdateDetectRule(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody SaveOrUpdateDetectRule.Arg arg) {
        return aiModelService.saveOrUpdateDetectRule(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/deleteDetectRule")
    public DeleteDetectRule.Result deleteDetectRule(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody DeleteDetectRule.Arg arg) {
        return aiModelService.deleteDetectRule(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/batchQueryModel")
    public BatchQueryModel.Result batchQueryModel(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody BatchQueryModel.Arg arg) {
        return aiModelService.batchQueryModel(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/batchQueryAIRuleByIds")
    public BatchQueryAIRuleByIds.Result batchQueryAIRuleByIds(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody BatchQueryAIRuleByIds.Arg arg) {
        return aiModelService.batchQueryAIRuleByIds(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    /**
     * 根据ID查询单个AI规则
     */
    @PostMapping("/getAIRuleById")
    public GetAIRuleById.Result getAIRuleById(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody GetAIRuleById.Arg arg) {
        return aiModelService.getAIRuleById(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    /**
     * 获取AI规则描述信息
     */
    @PostMapping("/getAIRuleDescribe")
    public GetAIRuleDescribe.Result getAIRuleDescribe(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody GetAIRuleDescribe.Arg arg) {
        return aiModelService.getAIRuleDescribe(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    @PostMapping("/getDisplayScenesByModelId")
    public GetDisplayScenesByModelId.Result getDisplayScenesByModelId(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody GetDisplayScenesByModelId.Arg arg) {
        return aiModelService.getDisplayScenesByModelId(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }

    /**
     * 添加Token信息
     */
    @PostMapping("/addToken")
    public AddToken.Result addToken(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody AddToken.Arg arg) {
        return aiModelService.addToken(ModelConverter.toServiceArg(userData, arg)).innerHandler();
    }
} 