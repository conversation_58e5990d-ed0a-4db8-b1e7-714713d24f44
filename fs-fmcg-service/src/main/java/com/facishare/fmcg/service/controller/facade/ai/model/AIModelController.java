package com.facishare.fmcg.service.controller.facade.ai.model;

import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.fmcg.api.dto.ai.model.AddModel;
import com.facishare.fmcg.api.dto.ai.model.AddToken;
import com.facishare.fmcg.api.dto.ai.model.BatchDeleteObjectMap;
import com.facishare.fmcg.api.dto.ai.model.BatchQueryAIRuleByIds;
import com.facishare.fmcg.api.dto.ai.model.BatchQueryModel;
import com.facishare.fmcg.api.dto.ai.model.BatchSaveOrUpdateObjectMap;
import com.facishare.fmcg.api.dto.ai.model.DeleteDetectRule;
import com.facishare.fmcg.api.dto.ai.model.GetAIModelScenes;
import com.facishare.fmcg.api.dto.ai.model.GetAIModelsByScene;
import com.facishare.fmcg.api.dto.ai.model.GetAIRuleById;
import com.facishare.fmcg.api.dto.ai.model.GetAIRuleDescribe;
import com.facishare.fmcg.api.dto.ai.model.GetDisplayScenesByModelId;
import com.facishare.fmcg.api.dto.ai.model.GetModelById;
import com.facishare.fmcg.api.dto.ai.model.GetModelDescribe;
import com.facishare.fmcg.api.dto.ai.model.ModelSwitch;
import com.facishare.fmcg.api.dto.ai.model.QueryObjectList;
import com.facishare.fmcg.api.dto.ai.model.QueryRuleList;
import com.facishare.fmcg.api.dto.ai.model.SaveOrUpdateDetectRule;
import com.facishare.fmcg.api.dto.ai.model.UpdateModel;
import com.facishare.fmcg.api.service.ai.model.AIModelService;
import com.facishare.fmcg.service.common.ModelConverter;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Slf4j
@RestController
@RequestMapping("ai/model")
@Api(tags = "AI模型管理接口")
public class AIModelController {

    @Resource
    private AIModelService aiModelService;

    @ApiOperation("获取AI模型场景列表")
    @RequestMapping("/scenes")
    public GetAIModelScenes.Result getAIModelScenes(@FSUserInfo UserInfo userInfo ,@RequestBody GetAIModelScenes.Arg arg) {
        return aiModelService.getAIModelScenes(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("根据场景获取AI模型列表")
    @RequestMapping("/getAIModelsByScene")
    public GetAIModelsByScene.Result getAIModelsByScene(
            @FSUserInfo UserInfo userInfo,
            @RequestBody GetAIModelsByScene.Arg arg
    ) {
        return aiModelService.getAIModelsByScene(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("根据ID获取AI模型详情")
    @RequestMapping("/getModelById")
    public GetModelById.Result getModelById(
            @FSUserInfo UserInfo userInfo,
            @RequestBody GetModelById.Arg arg
    ) {
        return aiModelService.getModelById(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("切换AI模型状态")
    @RequestMapping("/modelSwitch")
    public ModelSwitch.Result modelSwitch(
            @FSUserInfo UserInfo userInfo,
            @RequestBody ModelSwitch.Arg arg
    ) {
        return aiModelService.modelSwitch(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("获取模型描述信息")
    @RequestMapping("/getModelDescribe")
    public GetModelDescribe.Result getModelDescribe(
            @FSUserInfo UserInfo userInfo,
            @RequestBody GetModelDescribe.Arg arg
    ) {
        return aiModelService.getModelDescribe(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("添加AI模型")
    @RequestMapping("/addModel")
    public AddModel.Result addModel(
            @FSUserInfo UserInfo userInfo,
            @RequestBody AddModel.Arg arg
    ) {
        return aiModelService.addModel(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("更新AI模型")
    @RequestMapping("/updateModel")
    public UpdateModel.Result updateModel(
            @FSUserInfo UserInfo userInfo,
            @RequestBody UpdateModel.Arg arg
    ) {
        return aiModelService.updateModel(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("查询对象映射列表")
    @RequestMapping("/queryObjectList")
    public QueryObjectList.Result queryObjectList(
            @FSUserInfo UserInfo userInfo,
            @RequestBody QueryObjectList.Arg arg
    ) {
        return aiModelService.queryObjectList(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("批量删除对象映射")
    @RequestMapping("/batchDeleteObjectMap")
    public BatchDeleteObjectMap.Result batchDeleteObjectMap(
            @FSUserInfo UserInfo userInfo,
            @RequestBody BatchDeleteObjectMap.Arg arg
    ) {
        return aiModelService.batchDeleteObjectMap(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("批量保存或更新对象映射")
    @RequestMapping("/batchSaveOrUpdateObjectMap")
    public BatchSaveOrUpdateObjectMap.Result batchSaveOrUpdateObjectMap(
            @FSUserInfo UserInfo userInfo,
            @RequestBody BatchSaveOrUpdateObjectMap.Arg arg
    ) {
        return aiModelService.batchSaveOrUpdateObjectMap(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("查询规则列表")
    @RequestMapping("/queryRuleList")
    public QueryRuleList.Result queryRuleList(
            @FSUserInfo UserInfo userInfo,
            @RequestBody QueryRuleList.Arg arg
    ) {
        return aiModelService.queryRuleList(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("保存或更新检测规则")
    @RequestMapping("/saveOrUpdateDetectRule")
    public SaveOrUpdateDetectRule.Result saveOrUpdateDetectRule(
            @FSUserInfo UserInfo userInfo,
            @RequestBody SaveOrUpdateDetectRule.Arg arg
    ) {
        return aiModelService.saveOrUpdateDetectRule(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("删除检测规则")
    @RequestMapping("/deleteDetectRule")
    public DeleteDetectRule.Result deleteDetectRule(
            @FSUserInfo UserInfo userInfo,
            @RequestBody DeleteDetectRule.Arg arg
    ) {
        return aiModelService.deleteDetectRule(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("批量查询AI模型")
    @RequestMapping("/batchQueryModel")
    public BatchQueryModel.Result batchQueryModel(
            @FSUserInfo UserInfo userInfo,
            @RequestBody BatchQueryModel.Arg arg
    ) {
        return aiModelService.batchQueryModel(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("批量查询AI规则")
    @RequestMapping("/batchQueryAIRuleByIds")
    public BatchQueryAIRuleByIds.Result batchQueryAIRuleByIds(
            @FSUserInfo UserInfo userInfo,
            @RequestBody BatchQueryAIRuleByIds.Arg arg
    ) {
        return aiModelService.batchQueryAIRuleByIds(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("根据ID查询单个AI规则")
    @RequestMapping("/getAIRuleById")
    public GetAIRuleById.Result getAIRuleById(
            @FSUserInfo UserInfo userInfo,
            @RequestBody GetAIRuleById.Arg arg
    ) {
        return aiModelService.getAIRuleById(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("获取AI规则描述信息")
    @RequestMapping("/getAIRuleDescribe")
    public GetAIRuleDescribe.Result getAIRuleDescribe(
            @FSUserInfo UserInfo userInfo,
            @RequestBody GetAIRuleDescribe.Arg arg
    ) {
        return aiModelService.getAIRuleDescribe(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("根据模型ID获取场景列表")
    @RequestMapping("/getDisplayScenesByModelId")
    public GetDisplayScenesByModelId.Result getDisplayScenesByModelId(
            @FSUserInfo UserInfo userInfo,
            @RequestBody GetDisplayScenesByModelId.Arg arg
    ) {
        return aiModelService.getDisplayScenesByModelId(ModelConverter.convert(userInfo, arg)).cepHandler();
    }

    @ApiOperation("添加Token信息")
    @RequestMapping("/addToken")
    public AddToken.Result addToken(
            @FSUserInfo UserInfo userInfo,
            @RequestBody AddToken.Arg arg
    ) {
        return aiModelService.addToken(ModelConverter.convert(userInfo, arg)).cepHandler();
    }
} 