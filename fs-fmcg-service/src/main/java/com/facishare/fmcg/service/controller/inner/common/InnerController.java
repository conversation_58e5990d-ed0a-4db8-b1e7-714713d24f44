package com.facishare.fmcg.service.controller.inner.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.fmcg.api.annotation.FMCGUserInfo;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.charge.SynLicenseToChargeDetail;
import com.facishare.fmcg.api.dto.common.metadata.AddMetadataFiled;
import com.facishare.fmcg.api.dto.common.organization.AdminInit;
import com.facishare.fmcg.api.dto.common.organization.AppGray;
import com.facishare.fmcg.api.dto.common.organization.RebuildScene;
import com.facishare.fmcg.api.dto.common.organization.StoreWriteOffInit;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.model.sys.UserData;
import com.facishare.fmcg.api.service.ai.charge.AIChargeDetailService;
import com.facishare.fmcg.api.service.common.AppGraService;
import com.facishare.fmcg.api.service.common.MetadataInnerService;
import com.facishare.fmcg.api.service.common.RoleInitService;
import com.facishare.fmcg.api.service.tpmupgrade.TPMUpgradeService;
import com.facishare.fmcg.api.util.I18N;
import com.facishare.fmcg.provider.business.abstraction.*;
import com.facishare.fmcg.provider.business.model.AccountBO;
import com.facishare.fmcg.provider.business.model.AccountDetailBO;
import com.facishare.fmcg.provider.business.model.BizCallNumberBO;
import com.facishare.fmcg.provider.business.model.PriceBO;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.impl.auto.AutoInitService;
import com.facishare.fmcg.provider.impl.inner.DataUpdateService;
import com.facishare.fmcg.service.common.ModelConverter;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/27 上午10:36
 */
@RestController
@RequestMapping(value = "/innerService")
public class InnerController {

    @Resource
    private MetadataInnerService metadataInnerService;

    @Resource
    private AccountBusiness accountBusiness;

    @Resource
    private DataUpdateService dataUpdateService;

    @Resource
    private AIChargeDetailService aiChargeDetailService;
    @Resource
    private AppGraService appGraService;
    @Resource
    private RoleInitService roleInitService;
    @Resource
    private TPMUpgradeService tpmUpgradeService;
    @Resource
    private AutoInitService autoInitService;
    @Resource
    private SalesOrderBusiness salesOrderBusiness;
    @Resource
    private StoreWriteOffBusiness storeWriteOffBusiness;
    @Resource
    private DescribeBusiness describeBusiness;
    @Resource
    private TPMActivityMaterialBusiness tpmActivityMaterialBusiness;
    @Resource
    private TPMActivityDisplayReportBusiness tpmActivityDisplayReportBusiness;

    @Resource
    private LicenseDAO licenseDAO;


    @RequestMapping(value = "addMetadataField")
    public ApiResult<AddMetadataFiled.Result> addMetadataField(@RequestBody ApiArg<AddMetadataFiled.Arg> arg) {
        return metadataInnerService.addMetadataField(arg);
    }

    @RequestMapping(value = "addAccount")
    public AccountBO addAccount(@RequestBody AccountBO accountBO) {
        return accountBusiness.addAccount(accountBO);
    }

    @RequestMapping(value = "addAccountDetail")
    public AccountDetailBO addAccountDetail(@RequestBody AccountDetailBO accountDetailBO) {
        return accountBusiness.addAccountDetail(accountDetailBO);
    }

    @RequestMapping(value = "addPrice")
    public PriceBO addPrice(@RequestBody PriceBO priceBO) {
        return accountBusiness.addPrice(priceBO);
    }

    @RequestMapping(value = "addBizCallNumber")
    public BizCallNumberBO addBizCallNumber(@RequestBody BizCallNumberBO bizCallNumberBO) {
        return accountBusiness.addBizCallNumber(bizCallNumberBO);
    }

    @RequestMapping(value = "initPileDetect")
    public void initPileDetect(@RequestParam Integer tenantId, @RequestParam String biz, @RequestParam String modelId, @RequestParam Double unitPrice) {
        accountBusiness.init(tenantId, biz, modelId, unitPrice);
    }

    @RequestMapping(value = "updateCheckins")
    public String updateCheckins(@RequestBody JSONObject arg) {
        return dataUpdateService.updateCheckinsMonthGoal(arg);
    }

    @RequestMapping(value = "updatePersonnelSelfie")
    public String updatePersonnelSelfie(@RequestParam String tenantAccount) {
        return dataUpdateService.updateSelfieImage(tenantAccount);
    }

    @RequestMapping(value = "/synLicenseToChargeDetail", method = RequestMethod.POST)
    public SynLicenseToChargeDetail.Result synLicenseToChargeDetail(@FMCGUserInfo UserData userInfo, @RequestBody SynLicenseToChargeDetail.Arg arg) throws FmcgException {
        return aiChargeDetailService.synLicenseToChargeDetail(ModelConverter.toServiceArg(userInfo, arg)).cepHandler();
    }

    @RequestMapping(value = "/addGrayAndInitializationTemplate", method = RequestMethod.POST)
    public AppGray.Result addGrayAndInitializationTemplate(@FMCGUserInfo UserData userInfo, @RequestBody AppGray.Arg arg) {
        return appGraService.addGrayAndInitializationTemplate(ModelConverter.toServiceArg(userInfo, arg)).cepHandler();
    }

    @RequestMapping(value = "/preAdminTemplate", method = RequestMethod.POST)
    public AdminInit.Result preAdminTemplate(@FMCGUserInfo UserData userInfo, @RequestBody AdminInit.Arg arg) {
        return roleInitService.preAdminTemplate(ModelConverter.toServiceArg(userInfo, arg)).cepHandler();
    }

    @SneakyThrows
    @PostMapping(value = "/addAgreementVisitField")
    public String addAgreementVisitField() {
        return dataUpdateService.agreementVisitFieldAdd();
    }

    @RequestMapping(value = "/preUpgradeFields", method = RequestMethod.POST)
    public String upgradeFields(@RequestParam int targetTenantId) throws FmcgException {
        return tpmUpgradeService.preUpgradeFields(targetTenantId);
    }

    @RequestMapping(value = "/upgradeFields", method = RequestMethod.POST)
    public String upgradeFields(@RequestParam int targetTenantId, @RequestParam String beanName) throws FmcgException {
        return tpmUpgradeService.upgradeFields(targetTenantId, beanName);
    }

    @RequestMapping(value = "/write", method = RequestMethod.POST)
    public String write(@RequestParam int targetTenantId) throws FmcgException {
        return tpmUpgradeService.write(targetTenantId, "1");
    }

    @RequestMapping(value = "/upgradeLayout", method = RequestMethod.POST)
    public String upgradeLayout(@RequestParam int targetTenantId) throws FmcgException, IOException {
        return tpmUpgradeService.upgradeLayout(targetTenantId);
    }

    @RequestMapping(value = "/upgradeLicense", method = RequestMethod.POST)
    public String upgradeLicense(@RequestParam int targetTenantId) {
        return tpmUpgradeService.upgradeLicense(targetTenantId);
    }

    @RequestMapping(value = "/autoInitTpm", method = RequestMethod.POST)
    public String autoInitTpm(@RequestParam int tenantId) {
        autoInitService.autoTPM(tenantId);
        return "success";
    }

    @RequestMapping(value = "/addSalesOrderTPMField", method = RequestMethod.POST)
    public String addSalesOrderTPMField(@RequestParam int tenantId) {
        salesOrderBusiness.addSalesOrderTPMField(null, tenantId);
        return "success";
    }

    @RequestMapping(value = "/addStoreWriteOff", method = RequestMethod.POST)
    public String addStoreWriteOff(@RequestBody StoreWriteOffInit.Arg arg) {
        storeWriteOffBusiness.addStoreWriteOff(arg);
        return "success";
    }

    @RequestMapping(value = "/updateField", method = RequestMethod.POST)
    public String updateField(@RequestBody StoreWriteOffInit.Arg arg) {
        storeWriteOffBusiness.updateActivityType(arg);
        return "success";
    }

    @RequestMapping(value = "/addRefField", method = RequestMethod.POST)
    public String addRefField(@RequestParam int tenantId) {
        storeWriteOffBusiness.addRefField(tenantId);
        return "success";
    }

    @RequestMapping(value = "/addStoreWriteOffField", method = RequestMethod.POST)
    public String addStoreWriteOffField(@RequestBody StoreWriteOffInit.Arg arg) {
        storeWriteOffBusiness.addStoreWriteOffField(arg);
        return "success";
    }

    @RequestMapping(value = "/updateSubjectDescribe", method = RequestMethod.POST)
    public String updateSubjectDescribe(@RequestParam String tenantIds, @RequestParam String apiName) {
        dataUpdateService.updateSubjectDescribe(tenantIds, apiName);
        return "success";
    }

    @RequestMapping(value = "/deleteFieldByFieldName", method = RequestMethod.POST)
    public String deleteFieldByFieldName(@RequestParam String tenantIds, @RequestParam String apiName, @RequestParam String fieldName, @RequestParam String updateFieldFlag) {
        dataUpdateService.deleteFieldByFieldName(tenantIds, apiName, fieldName, updateFieldFlag);
        return "success";
    }

    @RequestMapping(value = "/initObject", method = RequestMethod.POST)
    public String initObject(@RequestParam String tenantIds, @RequestParam String apiNames, @RequestParam String path) {
        dataUpdateService.initObject(tenantIds, apiNames, path);
        return "success";
    }

    @RequestMapping(value = "/addFields", method = RequestMethod.POST)
    public String addFields(@RequestParam String tenantIds, @RequestBody JSONArray arg) {
        dataUpdateService.addFields(tenantIds, arg);
        return "success";
    }

    /**
     * @param tenantId
     * @param param    : 格式：roleCode:rolePermission
     * @return
     */
    @RequestMapping(value = "/initRolePermisson", method = RequestMethod.POST)
    public String deleteFieldByFieldName(@RequestParam Integer tenantId, @RequestParam String param) {
        roleInitService.fixInitRolePermission(tenantId, param);
        return "success";
    }

    @RequestMapping(value = "/initCashingFieldFor840", method = RequestMethod.POST)
    public String initCashingFieldFor840(@RequestParam List<Integer> tenantIds, @RequestParam String fromFile) {
        describeBusiness.initCashingFieldFor840(tenantIds, fromFile);
        return "success";
    }

    @RequestMapping(value = "/initCashingFieldFor850", method = RequestMethod.POST)
    public String initCashingFieldFor850(@RequestParam String tenantIds, @RequestParam String fromFile) {
        describeBusiness.initCashingFieldFor850(tenantIds, fromFile);
        return "success";
    }

    @RequestMapping(value = "/handlerFieldFor860", method = RequestMethod.POST)
    public String initCashingFieldFor860(@RequestParam List<Integer> tenantIds, @RequestParam(required = false) String fromFile, @RequestParam(required = false) String option, @RequestParam String appCode) {
        describeBusiness.handlerFieldFor860(tenantIds, fromFile, option, appCode);
        return "success";
    }

    @RequestMapping(value = "/handlerDealerCostFieldFor870", method = RequestMethod.POST)
    public String handlerDealerCostFieldFor870(@RequestParam List<Integer> tenantIds, @RequestParam(required = false) String fromFile) {
        describeBusiness.handlerDealerCostFieldFor870(tenantIds, fromFile);
        return "success";
    }

    @RequestMapping(value = "/handlerMNWithdrawObjAndFields", method = RequestMethod.POST)
    public String handlerMNWithdrawObjAndFields(@RequestBody JSONObject arg) {

        List<Integer> tenantIds = arg.getJSONArray("tenantIds").toJavaList(Integer.class);
        List<String> apiNames = arg.getJSONArray("apiNames").toJavaList(String.class);
        List<String> fieldNames = arg.getJSONArray("fieldNames").toJavaList(String.class);

        describeBusiness.handlerMNWithdrawObjAndFields(tenantIds, apiNames, fieldNames);
        return "success";
    }

    @RequestMapping(value = "/handlerMNRecordObjAndFields", method = RequestMethod.POST)
    public String handlerMNRecordObjAndFields(@RequestParam List<Integer> tenantIds) {
        describeBusiness.handlerMNRecordObjAndFields(tenantIds);
        return "success";
    }

    @RequestMapping(value = "/openFlowLayout", method = RequestMethod.POST)
    public String openFlowLayout(@RequestParam List<Integer> tenantIds, @RequestParam(required = false) String fromFile) {
        describeBusiness.openFlowLayout(tenantIds, fromFile);
        return "success";
    }

    @RequestMapping(value = "/handlerUpdateFieldFor860", method = RequestMethod.POST)
    public String handlerUpdateFieldFor860(@RequestBody JSONObject arg) {

        List<Integer> tenantIds = arg.getJSONArray("tenantIds").toJavaList(Integer.class);
        String source = arg.getString("source");
        String configName = arg.getString("configName");
        String fieldUpdate = arg.getString("fieldUpdate");

        describeBusiness.handlerUpdateFieldFor860(tenantIds, source, configName, fieldUpdate);
        return "success";
    }

    @RequestMapping(value = "/initUnifiedCaseFor850", method = RequestMethod.POST)
    public String initUnifiedCaseFor850(@RequestBody StoreWriteOffInit.Arg arg) {
        describeBusiness.initUnifiedCaseFor850(arg);
        return "success";
    }

    @RequestMapping(value = "/modifyFieldNameFor850", method = RequestMethod.POST)
    public String modifyFieldNameFor850(@RequestParam String tenantIds,
                                        @RequestParam String fromFile,
                                        @RequestParam String option) {
        describeBusiness.modifyFieldNameFor850(tenantIds, fromFile, option);
        return "success";
    }

    @RequestMapping(value = "/initRoleCopyByMould", method = RequestMethod.POST)
    public String initRoleCopyByMould(@RequestParam String sourceId,
                                      @RequestParam String tenantIds,
                                      @RequestParam String roleCode) {
        describeBusiness.initRoleCopyByMould(sourceId, tenantIds, roleCode);
        return "success";
    }

    @RequestMapping(value = "/testParams", method = RequestMethod.POST)
    public String initObject(@RequestParam(required = false, defaultValue = "{}") String values) {
        JSON.parseObject(values).get("a");
        return values;
    }

    @RequestMapping(value = "/colorUpdate", method = RequestMethod.POST)
    public String colorUpdate(@RequestBody JSONObject arg) {
        return dataUpdateService.updateOptionColor(arg.getObject("tenantIds", new TypeReference<List<String>>() {
        }), arg.getObject("fields", new TypeReference<List<String>>() {
        }));
    }

    @RequestMapping(value = "/invalidLicense")
    public String invalidLicense(@RequestBody JSONObject arg) {
        licenseDAO.invalid(arg.getInteger("tenant_id"), arg.getString("app_code"));
        return "success";
    }

    @RequestMapping(value = "/deleteLicense")
    public String deleteLicense(@RequestBody JSONObject arg) {
        licenseDAO.delete(arg.getInteger("tenant_id"), arg.getString("app_code"));
        return "success";
    }

    @RequestMapping(value = "/updateFieldDescribe", method = RequestMethod.POST)
    public String updateFieldDescribe(@RequestBody JSONObject arg) {
        return dataUpdateService.updateFieldDescribe(arg.getInteger("tenant_id"), arg.getObject("field_describe_map", Map.class));
    }

    @RequestMapping(value = "/batchUpdateFieldDescribe", method = RequestMethod.POST)
    public String batchUpdateFieldDescribe(@RequestBody JSONObject arg) {
        List<Integer> tenantIds = arg.getObject("tenant_ids", new TypeReference<List<Integer>>() {
        });
        Map fieldMap = arg.getObject("field_describe_map", Map.class);
        Map<Integer, String> result = new HashMap<>();
        tenantIds.forEach(tenantId -> {
            result.put(tenantId, dataUpdateService.updateFieldDescribe(tenantId, fieldMap));
        });
        return JSON.toJSONString(result);
    }

    @RequestMapping(value = "/addDescribeField", method = RequestMethod.POST)
    public String addDescribeField(@RequestBody JSONObject arg) {
        return dataUpdateService.addDescribeField(arg.getObject("tenant_ids", new TypeReference<List<Integer>>() {
        }), arg.getObject("field_describe_map", Map.class));
    }

    @RequestMapping(value = "/invalidDescribeField", method = RequestMethod.POST)
    public String invalidDescribeField(@RequestBody JSONObject arg) {
        return dataUpdateService.invalidDescribeField(arg.getObject("tenant_ids", new TypeReference<List<Integer>>() {
        }), arg.getObject("field_describe_map", Map.class));
    }

    @RequestMapping(value = "/batchUpdateFieldDescribeWithLayout", method = RequestMethod.POST)
    public String updateFieldDescribeWithLayout(@RequestBody JSONObject arg) {

        List<Integer> tenantIds = arg.getObject("tenant_ids", new TypeReference<List<Integer>>() {
        });
        Map fieldMap = arg.getObject("field_describe_map", Map.class);
        Map<Integer, String> result = new HashMap<>();
        tenantIds.forEach(tenantId -> {
            result.put(tenantId, dataUpdateService.updateFieldDescribeWithLayout(tenantId, fieldMap));
        });
        return JSON.toJSONString(result);
    }

    @RequestMapping(value = "/initExpenseObj", method = RequestMethod.POST)
    public String initExpenseObj(@RequestBody JSONObject arg) {
        autoInitService.createExpenseClaimObj(arg.getInteger("tenant_id"));
        return "success";
    }

    @RequestMapping(value = "/addTPMActivityMaterialObjField", method = RequestMethod.POST)
    public String addTPMActivityMaterialObjField(@RequestParam int tenantId) {
        tpmActivityMaterialBusiness.addTPMActivityMaterialObjField(tenantId);
        return "success";
    }

    @RequestMapping(value = "/addTPMActivityMaterialObjAndField", method = RequestMethod.POST)
    public String addTPMActivityMaterialObjAndField(@RequestParam int tenantId) {
        tpmActivityMaterialBusiness.addTPMActivityMaterialObjAndField(tenantId);
        return "success";
    }

    @RequestMapping(value = "/addAllTPMActivityMaterialObjAndField", method = RequestMethod.POST)
    public String addAllTPMActivityMaterialObjAndField() {
        tpmActivityMaterialBusiness.addAllTPMActivityMaterialObjAndField();
        return "success";
    }

    @RequestMapping(value = "/initObjFor860", method = RequestMethod.POST)
    public String initObjFor860() {
        tpmActivityMaterialBusiness.initObjFor860();
        return "success";
    }

    @RequestMapping(value = "/initObjForPhysicalRewards", method = RequestMethod.POST)
    public String initObjForPhysicalRewards(@RequestBody JSONObject arg) {
        List<Integer> tenantIds = arg.getJSONArray("tenantIds").toJavaList(Integer.class);
        List<String> apiNames = arg.getJSONArray("apiNames").toJavaList(String.class);
        describeBusiness.initObjForPhysicalRewards(tenantIds, apiNames);
        return "success";
    }

    @RequestMapping(value = "/initBudgetProvision", method = RequestMethod.POST)
    public String initObjectDescription(@RequestBody JSONObject arg) {
        List<Integer> tenantIds = arg.getJSONArray("tenantIds").toJavaList(Integer.class);
        List<String> apiNames = arg.getJSONArray("apiNames").toJavaList(String.class);
        String source = arg.getString("source");
        String configName = arg.getString("configName");
        describeBusiness.initObjectDescription(tenantIds, apiNames, source, configName);
        return "success";
    }

    @RequestMapping(value = "/autoCopyCustomPage", method = RequestMethod.POST)
    public String autoCopyCustomPage(@RequestParam int tenantId) {
        autoInitService.autoCopyCustomPage(tenantId);
        return "success";
    }

    @RequestMapping(value = "/initRebuildScene", method = RequestMethod.POST)
    public String initRebuildScene(@RequestBody RebuildScene.Arg arg) {
        describeBusiness.initRebuildScene(arg);
        return "success";
    }


    @RequestMapping(value = "fixAgreementLayout")
    public String fixAgreementLayout(@RequestBody JSONObject arg) {
        dataUpdateService.fixAgreementLayout(arg.getInteger("tenantId"));
        return "success";
    }

    @RequestMapping(value = "/addTPMActivityDisplayReportObjAndFields", method = RequestMethod.POST)
    public String addTPMActivityDisplayReportObjAndFields(@RequestBody JSONObject arg) {
        List<Integer> tenantIds = arg.getJSONArray("tenantIds").toJavaList(Integer.class);
        tpmActivityDisplayReportBusiness.addTPMActivityDisplayReportObjAndFields(tenantIds);
        return "success";
    }

    @RequestMapping(value = "/deleteTPMActivityDisplayReportObjAndFields", method = RequestMethod.POST)
    public String deleteTPMActivityDisplayReportObjAndFields(@RequestBody JSONObject arg) {
        List<Integer> tenantIds = arg.getJSONArray("tenantIds").toJavaList(Integer.class);
        tpmActivityDisplayReportBusiness.deleteTPMActivityDisplayReportObjAndFields(tenantIds);
        return "success";
    }

    @RequestMapping(value = "/i18n")
    public String i18n() {
        return I18N.getOrDefaultByOriginalKey("fmcg.model.manufacturer.baidu","1");
    }
}
