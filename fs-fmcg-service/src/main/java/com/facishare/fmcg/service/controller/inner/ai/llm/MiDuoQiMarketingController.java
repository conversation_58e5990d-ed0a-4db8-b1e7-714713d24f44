package com.facishare.fmcg.service.controller.inner.ai.llm;

import com.facishare.fmcg.api.annotation.FMCGUserInfo;
import com.facishare.fmcg.api.dto.ai.detect.MiDuoQiMarketingDetect;
import com.facishare.fmcg.api.model.sys.UserData;
import com.facishare.fmcg.api.service.ai.detect.IMiDuoQiMarketingService;
import com.facishare.fmcg.service.common.ModelConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/MiDuoQi/Marketing")
public class MiDuoQiMarketingController {

    @Resource
    private IMiDuoQiMarketingService miDuoQiMarketingService;

    @PostMapping(value = "/Detect")
    public MiDuoQiMarketingDetect.Result detect(@FMCGUserInfo UserData userInfo, @RequestBody MiDuoQiMarketingDetect.Arg arg) {
        return miDuoQiMarketingService.detect(ModelConverter.toServiceArg(userInfo, arg)).innerHandler();
    }
}