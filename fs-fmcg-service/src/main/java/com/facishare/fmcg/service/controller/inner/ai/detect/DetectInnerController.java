package com.facishare.fmcg.service.controller.inner.ai.detect;

import com.facishare.fmcg.api.annotation.FMCGOuterUserInfo;
import com.facishare.fmcg.api.annotation.FMCGUserInfo;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.detect.*;
import com.facishare.fmcg.api.model.sys.OuterUserData;
import com.facishare.fmcg.api.model.sys.UserData;
import com.facishare.fmcg.api.service.ai.detect.CustomAIDetectService;
import com.facishare.fmcg.api.service.ai.detect.CommonAIDetectService;
import com.facishare.fmcg.service.common.ModelConverter;
import com.fs.fmcg.sdk.ai.DetectClient;
import com.fs.fmcg.sdk.ai.contract.Detect;
import com.fs.fmcg.sdk.ai.contract.PureDetect;
import com.fs.fmcg.sdk.ai.contract.SdkContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/4/25 下午6:07
 */
@Slf4j
@RestController
@RequestMapping(value = "/detect")
public class DetectInnerController {

    @Resource
    private CustomAIDetectService customAIDetectService;

    @Resource
    private CommonAIDetectService commonAIDetectService;

    @Resource
    private DetectClient detectClient;

    @PostMapping(value = "/dz_detect")
    public DZDetectByScene.Result dzAIDetect(@FMCGUserInfo UserData userData, @FMCGOuterUserInfo OuterUserData outerUserData, @RequestBody DZDetectByScene.Arg arg) {
        return customAIDetectService.dzDetectByScene(ModelConverter.toServiceArg(userData, arg)).cepHandler();
    }

    @PostMapping(value = "/save_dz_detect_result")
    public SaveDZDetectRecord.Result saveDZDetectResult(@FMCGUserInfo UserData userData, @FMCGOuterUserInfo OuterUserData outerUserData, @RequestBody SaveDZDetectRecord.Arg arg) {
        return customAIDetectService.saveDZDetectRecord(ModelConverter.toServiceArg(userData, arg)).cepHandler();
    }

    @PostMapping(value = "/common_detect")
    public PureDetect.Result commonDetect(@FMCGUserInfo UserData userData, @FMCGOuterUserInfo OuterUserData outerUserData, @RequestBody PureDetect.Arg arg) {
        return detectClient.detectWithModel(SdkContext.builder().tenantId(userData.getTenantId()).tenantAccount(userData.getTenantAccount()).currentEmployeeId(userData.getUserId()).build(), arg);
    }

    @PostMapping(value = "/checkin_report_detect")
    public Detect.Result commonDetect(@FMCGUserInfo UserData userData, @FMCGOuterUserInfo OuterUserData outerUserData, @RequestBody Detect.Arg arg) {
        return detectClient.detect(SdkContext.builder().tenantId(userData.getTenantId()).tenantAccount(userData.getTenantAccount()).currentEmployeeId(userData.getUserId()).build(), arg);
    }

    @PostMapping(value = "/hly_recapture")
    public HLYProofRecapture.Result hlyRecapture(@FMCGUserInfo UserData userData, @FMCGOuterUserInfo OuterUserData outerUserData, @RequestBody HLYProofRecapture.Arg arg) {
        return customAIDetectService.hlyProofRecapture(ModelConverter.toServiceArg(userData, arg)).cepHandler();
    }

    @PostMapping(value = "/real_name_face_auth")
    public ApiResult<RealNameFaceAuth.Result> realNameFaceAuth(@FMCGUserInfo UserData userData, @FMCGOuterUserInfo OuterUserData outerUserData, @RequestBody RealNameFaceAuth.Arg arg) {
        return customAIDetectService.realNameFaceAuth(ModelConverter.toServiceArg(userData, arg));
    }

    /**
     * 门头识别接口
     * @param userData 用户数据
     * @param outerUserData 外部用户数据
     * @param arg 门头识别请求参数
     * @return 门头识别结果
     */
    @PostMapping("/storefront")
    public ApiResult<CommonAIDetect.Result> storeFrontDetect(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody CommonAIDetect.Arg arg) {

        log.info("[StoreFrontDetectInnerController] 接收门头识别请求，用户: {}, 参数: {}",
                userData.getUserId(), arg);

        return commonAIDetectService.commonAIDetect(ModelConverter.toServiceArg(userData, arg));
    }

    @PostMapping("/commonAIDetect")
    public ApiResult<CommonAIDetect.Result> commonAIDetect(
            @FMCGUserInfo UserData userData,
            @FMCGOuterUserInfo OuterUserData outerUserData,
            @RequestBody CommonAIDetect.Arg arg) {

        log.info("[commonAIDetect] 接收门头识别请求，用户: {}, 参数: {}",
                userData.getUserId(), arg);

        return commonAIDetectService.commonAIDetect(ModelConverter.toServiceArg(userData, arg));
    }
}
