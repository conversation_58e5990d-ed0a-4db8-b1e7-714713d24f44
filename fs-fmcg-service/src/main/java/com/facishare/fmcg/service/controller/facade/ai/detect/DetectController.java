package com.facishare.fmcg.service.controller.facade.ai.detect;

import com.facishare.cep.plugin.annotation.FSOuterUserInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.fmcg.api.dto.ai.detect.DZDetectByScene;
import com.facishare.fmcg.api.dto.ai.detect.CommonAIDetect;
import com.facishare.fmcg.api.service.ai.detect.CommonAIDetectService;
import com.facishare.fmcg.api.service.ai.detect.CustomAIDetectService;
import com.facishare.fmcg.service.common.ModelConverter;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/4/25 下午6:07
 */
@Slf4j
@RestController
@RequestMapping(value = "/detect")
public class DetectController {

    @Resource
    private CustomAIDetectService customAIDetectService;

    @Resource
    private CommonAIDetectService commonAIDetectService;

    @RequestMapping(value = "/dz_detect")
    public DZDetectByScene.Result dzAIDetect(@FSOuterUserInfo OuterUserInfo outerUserInfo, @FSUserInfo UserInfo userInfo, @RequestBody DZDetectByScene.Arg arg) {
        return customAIDetectService.dzDetectByScene(ModelConverter.convert(userInfo, arg)).cepHandler();
    }


    @ApiOperation("门头识别")
    @PostMapping("/storefront")
    public CommonAIDetect.Result storeFrontDetect(
            @FSUserInfo UserInfo userInfo,
            @FSOuterUserInfo OuterUserInfo outerUserInfo,
            @RequestBody CommonAIDetect.Arg arg) {

        log.info("[StoreFrontDetectController] 接收门头识别请求，用户: {}, 参数: {}",
                userInfo.getEmployeeId(), arg);

        return commonAIDetectService.commonAIDetect(ModelConverter.convert(userInfo, arg)).cepHandler();
    }


    @ApiOperation("通用识别")
    @PostMapping("/commonAIDetect")
    public CommonAIDetect.Result commonAIDetect(
            @FSUserInfo UserInfo userInfo,
            @FSOuterUserInfo OuterUserInfo outerUserInfo,
            @RequestBody CommonAIDetect.Arg arg) {

        log.info("[commonAIDetect] 接收门头识别请求，用户: {}, 参数: {}",
                userInfo.getEmployeeId(), arg);

        return commonAIDetectService.commonAIDetect(ModelConverter.convert(userInfo, arg)).cepHandler();
    }
}
