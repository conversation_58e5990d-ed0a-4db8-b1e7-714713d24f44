package com.facishare.fmcg.api.dto.ai.detect;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public interface MiDuoQiMarketingDetect {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {

        private String id;

        @Override
        public void insure() {

        }

        @Override
        public void validate() throws FmcgException {

        }
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    @Builder
    class Result extends ResultBase {

        private String id;
    }
}
