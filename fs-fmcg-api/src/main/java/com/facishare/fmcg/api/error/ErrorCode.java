package com.facishare.fmcg.api.error;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */

public enum ErrorCode {
    WAREHOUSE_ERROR(100001, "仓库信息错误。", "FMCG.FMCG_SERVICE.WAREHOUSE_ERROR"),
    ARG_ERROR(100000, "参数错误。", "FMCG.FMCG_SERVICE.ARG_ERROR"),
    PERMISSION_CHECK_ERROR(100403, "功能权限校验失败。", "FMCG.FMCG_SERVICE.PERMISSION_CHECK_ERROR"),
    CAR_MEMBER_EXIST(100006, "该员工已经配置为车销人员，无法重复配置。", "FMCG.FMCG_SERVICE.CAR_MEMBER_EXIST"),
    CAR_MEMBER_VALIDATE_ERROR(100003, "身份验证错误，当前登陆人员不是车销人员。", "FMCG.FMCG_SERVICE.CAR_MEMBER_VALIDATE_ERROR"),
    ROUTE_NOT_FOUND(100011, "未找到有效的路线信息。", "FMCG.FMCG_SERVICE.ROUTE_NOT_FOUND"),
    CAR_MEMBER_NOT_FOUND(100004, "未找到对应的车销人员信息。", "FMCG.FMCG_SERVICE.CAR_MEMBER_NOT_FOUND"),
    WAREHOUSE_NOT_VISIBLE_ERROR(100002, "仓库不可见或该仓库不存在！", "FMCG.FMCG_SERVICE.WAREHOUSE_NOT_VISIBLE_ERROR"),
    RESPONSIBLE_WAREHOUSE_EXIST(100007, "车库冲突，已有车销人员关联该车库", "FMCG.FMCG_SERVICE.RESPONSIBLE_WAREHOUSE_EXIST"),
    DEALER_NOT_FOUND(100005, "经销商信息不存在", "FMCG.FMCG_SERVICE.DEALER_NOT_FOUND"),
    CODE_MISMATCHING(100009, "扫码失败。", "FMCG.FMCG_SERVICE.CODE_MISMATCHING"),
    TASK_TYPE_NOT_FOUND(200001, "找不到任务类型", "FMCG.FMCG_SERVICE.TASK_TYPE_NOT_FOUND"),
    TASK_TYPE_NOT_FILL(200002, "未填写任务类型", "FMCG.FMCG_SERVICE.TASK_TYPE_NOT_FILL"),
    TASK_WAIT_TOO_LONG(200003, "等待超时", "FMCG.FMCG_SERVICE.TASK_WAIT_TOO_LONG"),
    DISTRIBUTED_LOCK_ERROR(650, "操作分布式锁发生未知异常。", "FMCG.FMCG_SERVICE.DISTRIBUTED_LOCK_ERROR"),
    SYSTEM_ADMIN_NOT_FOUND(651, "系统管理员不存在。", "FMCG.FMCG_SERVICE.SYSTEM_ADMIN_NOT_FOUND"),
    APP_ADMIN_NOT_FOUND(652, "应用管理员没有匹配到人员。", "FMCG.FMCG_SERVICE.APP_ADMIN_NOT_FOUND"),
    CONFIG_KEY_NOT_FOUND(653, "需要更新的K不存在。", "FMCG.FMCG_SERVICE.CONFIG_KEY_NOT_FOUND"),
    APPEAL_TIMEDOUT_ERROR(200004, "照片拍摄时间在七天内且在当月最后一天24点前，可发起申诉，超过后不可发起申诉。", "FMCG.FMCG_SERVICE.APPEAL_TIMEDOUT_ERROR"),
    FACE_OCCLUSION(223113,"请勿遮挡面部!",""),
    DO_NOT_SHAKE_PHOTO_IS_VAGUE(223114, "照片模糊,请勿晃动手机!","DO_NOT_SHAKE_PHOTO_IS_VAGUE"),
    LIGHT_IS_LOW(223115,"请到光线适宜的地方拍摄!","LIGHT_IS_LOW"),
    FACE_OCCLUSION_2(223116,"请勿遮挡面部!","FACE_OCCLUSION_2"),
    FAKE_FACE(223120,"请勿用照片或画纸来替代人脸!","FAKE_FACE"),
    BLOCK_LEFT_EYE(223121,"请勿遮挡左眼!","BLOCK_LEFT_EYE"),
    BLOCK_RIGHT_EYE(223122,"请勿遮挡右眼!","BLOCK_RIGHT_EYE"),
    BLOCK_LEFT_CHEEK(223123,"请勿遮挡左脸颊!","BLOCK_LEFT_CHEEK"),
    BLOCK_RIGHT_CHEEK(223124,"请勿遮挡右脸颊!","BLOCK_RIGHT_CHEEK"),
    MASK_CHIN(223125,"请勿遮挡下巴!","MASK_CHIN"),
    MASK_NOSE(223126,"请勿遮挡鼻子!","MASK_NOSE"),
    MASK_MOUTH(223127,"请勿遮挡嘴巴!","MASK_MOUTH"),
    DETECT_OUT_NO_FACE(222202,"未能识别出人脸照","DETECT_OUT_NO_FACE"),
    UNABLE_TO_PARSE_FACE(222203,"无法解析人脸，请请重新拍摄!","UNABLE_TO_PARSE_FACE"),
    MULTI_FACE(230001,"存在多张人脸，请到无杂物的背景拍摄。","MULTI_FACE"),
    FACE_CONFIDENT_LOW(230002,"人脸置信度过低，请重新拍摄。","FACE_CONFIDENT_LOW"),
    COMPOSITE_FACE(230003,"人脸疑是合成图，请重新拍摄。","COMPOSITE_FACE"),
    OPEN_BOTH_EYE(230004,"请适当睁开双眼后重新拍摄。","OPEN_BOTH_EYE"),
    PARTIAL_FACE_MISSING(230005,"部分人脸不在相片内。","PARTIAL_FACE_MISSING"),
    FACE_TO_LENS(230006,"请正视镜头，请勿过度抬头或转头。","FACE_TO_LENS"),
    DETECT_FACE_ERROR(230007,"人脸检测异常，请重试。","DETECT_FACE_ERROR"),
    FACE_DETECT_EXPIRE(7000001,"AI自拍识别已到期，请联系企业管理员重新配置外勤动作","FACE_DETECT_EXPIRE"),
    MULTI_FACE_DETECT_FAIL(230008,"多次识别失败，如有疑问，请企信联系管理员!","MULTI_FACE_DETECT_FAIL"),
    UNKNOWN(500000,"未知异常","UNKNOWN"),
    DETECT_TIME_OUT(500001,"识别超时，请重试","DETECT_TIME_OUT"),
    
    // 参数错误 4xxxx 系列
    PARAM_DELETE_IDS_EMPTY(40001, "删除ID列表不能为空", "FMCG.FMCG_SERVICE.PARAM_DELETE_IDS_EMPTY"),
    PARAM_OBJECT_MAP_EMPTY(40002, "对象映射列表不能为空", "FMCG.FMCG_SERVICE.PARAM_OBJECT_MAP_EMPTY"),
    PARAM_EMPTY(40003, "参数不能为空", "FMCG.FMCG_SERVICE.PARAM_EMPTY"),
    
    // 系统错误 5xxxx 系列
    AI_MODEL_LIST_ERROR(50001, "获取AI模型列表失败", "FMCG.FMCG_SERVICE.AI_MODEL_LIST_ERROR"),
    MODEL_DETAIL_ERROR(50002, "获取模型详情失败", "FMCG.FMCG_SERVICE.MODEL_DETAIL_ERROR"),
    MODEL_STATUS_SWITCH_ERROR(50003, "切换模型状态失败", "FMCG.FMCG_SERVICE.MODEL_STATUS_SWITCH_ERROR"),
    MODEL_ADD_ERROR(50004, "添加模型失败", "FMCG.FMCG_SERVICE.MODEL_ADD_ERROR"),
    MODEL_UPDATE_ERROR(50005, "更新模型失败", "FMCG.FMCG_SERVICE.MODEL_UPDATE_ERROR"),
    MODEL_DESCRIPTION_ERROR(50006, "获取模型描述信息失败", "FMCG.FMCG_SERVICE.MODEL_DESCRIPTION_ERROR"),
    OBJECT_MAP_QUERY_ERROR(50007, "查询对象映射列表失败", "FMCG.FMCG_SERVICE.OBJECT_MAP_QUERY_ERROR"),
    OBJECT_MAP_DELETE_ERROR(50008, "批量删除对象映射失败", "FMCG.FMCG_SERVICE.OBJECT_MAP_DELETE_ERROR"),
    OBJECT_MAP_SAVE_UPDATE_ERROR(50009, "批量保存或更新对象映射失败", "FMCG.FMCG_SERVICE.OBJECT_MAP_SAVE_UPDATE_ERROR"),
    RULE_LIST_QUERY_ERROR(50010, "查询规则列表失败", "FMCG.FMCG_SERVICE.RULE_LIST_QUERY_ERROR"),
    AI_DETECT_RULE_ADD_ERROR(50011, "新增AI检测规则失败", "FMCG.FMCG_SERVICE.AI_DETECT_RULE_ADD_ERROR"),
    AI_DETECT_RULE_UPDATE_ERROR(50012, "更新AI检测规则失败", "FMCG.FMCG_SERVICE.AI_DETECT_RULE_UPDATE_ERROR"),
    SYSTEM_ERROR(50013, "系统异常", "FMCG.FMCG_SERVICE.SYSTEM_ERROR"),
    MODEL_BATCH_QUERY_ERROR(50014, "批量查询模型失败", "FMCG.FMCG_SERVICE.MODEL_BATCH_QUERY_ERROR"),
    AI_RULE_BATCH_QUERY_ERROR(50015, "批量查询AI规则失败", "FMCG.FMCG_SERVICE.AI_RULE_BATCH_QUERY_ERROR"),
    AI_DETECT_RULE_DELETE_ERROR(50016, "删除AI检测规则失败", "FMCG.FMCG_SERVICE.AI_DETECT_RULE_DELETE_ERROR"),
    AI_DETECT_DEFAULT_RULE_DELETE_ERROR(50017, "禁止删除预置规则", "FMCG.FMCG_SERVICE.AI_DETECT_DEFAULT_RULE_DELETE_ERROR"),
    STORE_FRONT_DETECT_ERROR(50018, "门头识别失败", "FMCG.FMCG_SERVICE.STORE_FRONT_DETECT_ERROR"),
    AI_MODEL_TYPE_NOT_SUPPORTED(50019, "暂不支持其他类型的模型", "FMCG.FMCG_SERVICE.AI_MODEL_TYPE_NOT_SUPPORTED"),
    PROMPT_TEMPLATE_NOT_FOUND(50020, "找不到提示词", "FMCG.FMCG_SERVICE.PROMPT_TEMPLATE_NOT_FOUND"),
    UNKNOWN_PROMPT_TYPE(50021, "未知提示词类型", "FMCG.FMCG_SERVICE.UNKNOWN_PROMPT_TYPE"),
    MISSING_REQUIRED_PARAMETER(50022, "缺少必填参数:%s", "FMCG.FMCG_SERVICE.MISSING_REQUIRED_PARAMETER"),
    MODEL_SCENE_MISMATCH(50023, "模型场景不匹配", "FMCG.FMCG_SERVICE.MODEL_SCENE_MISMATCH"),
    
    // 业务错误 6xxxxx 系列
    TOKEN_VALIDATION_ERROR(600101, "token校验失败", "FMCG.FMCG_SERVICE.TOKEN_VALIDATION_ERROR"),
    TOKEN_INFO_ERROR(600102, "获取token信息失败", "FMCG.FMCG_SERVICE.TOKEN_INFO_ERROR"),
    MODEL_ADD_BUSINESS_ERROR(600103, "添加模型失败", "FMCG.FMCG_SERVICE.MODEL_ADD_BUSINESS_ERROR"),
    MODEL_STATUS_SWITCH_BUSINESS_ERROR(600104, "切换模型状态失败", "FMCG.FMCG_SERVICE.MODEL_STATUS_SWITCH_BUSINESS_ERROR"),
    REAL_NAME_AUTH_ERROR(600105, "实名认证发生未知异常", "FMCG.FMCG_SERVICE.REAL_NAME_AUTH_ERROR"),
    OBJECT_MAP_UPDATE_BUSINESS_ERROR(600106, "批量更新对象映射失败", "FMCG.FMCG_SERVICE.OBJECT_MAP_UPDATE_BUSINESS_ERROR"),
    OBJECT_MAP_DELETE_BUSINESS_ERROR(600107, "批量删除对象映射失败", "FMCG.FMCG_SERVICE.OBJECT_MAP_DELETE_BUSINESS_ERROR"),
    OBJECT_MAP_ADD_BUSINESS_ERROR(600108, "批量添加对象映射失败", "FMCG.FMCG_SERVICE.OBJECT_MAP_ADD_BUSINESS_ERROR"),
    OBJECT_MAP_QUERY_BUSINESS_ERROR(600109, "根据ID查询对象映射失败", "FMCG.FMCG_SERVICE.OBJECT_MAP_QUERY_BUSINESS_ERROR"),
    AI_DETECT_RULE_ADD_BUSINESS_ERROR(600110, "添加AI检测规则失败", "FMCG.FMCG_SERVICE.AI_DETECT_RULE_ADD_BUSINESS_ERROR"),
    AI_DETECT_RULE_UPDATE_BUSINESS_ERROR(600111, "更新AI检测规则失败", "FMCG.FMCG_SERVICE.AI_DETECT_RULE_UPDATE_BUSINESS_ERROR"),
    AI_DETECT_RULE_LIST_QUERY_BUSINESS_ERROR(600112, "查询AI检测规则列表失败", "FMCG.FMCG_SERVICE.AI_DETECT_RULE_LIST_QUERY_BUSINESS_ERROR"),
    AI_DETECT_RULE_DELETE_BUSINESS_ERROR(600113, "删除AI检测规则失败", "FMCG.FMCG_SERVICE.AI_DETECT_RULE_DELETE_BUSINESS_ERROR"),
    AI_DETECT_RULE_QUERY_BUSINESS_ERROR(600114, "查询AI检测规则失败", "FMCG.FMCG_SERVICE.AI_DETECT_RULE_QUERY_BUSINESS_ERROR"),
    AI_DETECT_RULE_BATCH_QUERY_BUSINESS_ERROR(600115, "批量查询AI检测规则失败", "FMCG.FMCG_SERVICE.AI_DETECT_RULE_BATCH_QUERY_BUSINESS_ERROR"),
    NEED_OBJECT_MAP_AND_AI_DETECT_RULE(600116,"完成\"AI商品映射\"及\"AI业务规则\"才可启用模型！","NEED_OBJECT_MAP_AND_AI_DETECT_RULE"),
    NEED_AI_DETECT_RULE(600116,"完成\"AI业务规则\"才可启用模型！","NEED_AI_DETECT_RULE"),
    MODEL_HAS_BEEN_RELATED(600117,"该模型已被引用，不可关闭", "FMCG.FMCG_SERVICE.MODEL_HAS_BEEN_RELATED"),
    AI_DETECT_RULE_NOT_FOUND(600118, "查询不到AI规则", "FMCG.FMCG_SERVICE.AI_DETECT_RULE_NOT_FOUND"),
    OBJECT_MAP_KEY_CONFLICT(600119, "映射关系的key已经存在请不要重复添加，冲突的键值如下：%s", "FMCG.FMCG_SERVICE.OBJECT_MAP_KEY_CONFLICT"),
    RULE_HAS_BEEN_RELATED(600120, "无法删除!该规则已被业务动作引用", "FMCG.FMCG_SERVICE.RULE_HAS_BEEN_RELATED"),
    OBJECT_MAP_DO_NOT_HAS_OBJECT_ID(600121, "存在没有映射对象数据的标签，请核查。", "FMCG.FMCG_SERVICE.OBJECT_MAP_DO_NOT_HAS_OBJECT_ID"),
    MODEL_NOT_FOUND(600122,"查询不到对应的ai模型，请核查。", "FMCG.FMCG_SERVICE.MODEL_NOT_FOUND"),
    TOKEN_ADD_ERROR(600123, "添加Token失败", "FMCG.FMCG_SERVICE.TOKEN_ADD_ERROR")
    ;

    private int code;
    private String message;
    private String i18nKey;

    private static Map<Integer, ErrorCode> innerMap = Stream.of(ErrorCode.values()).collect(Collectors.toMap(ErrorCode::getCode, v -> v,(before,after)->before));

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    ErrorCode(int code, String message,String i18nKey) {
        this.code = code;
        this.message = message;
        this.i18nKey = i18nKey;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getI18nKey(){return i18nKey;}

    public static ErrorCode of(int code) {
        return innerMap.get(code);
    }
}
