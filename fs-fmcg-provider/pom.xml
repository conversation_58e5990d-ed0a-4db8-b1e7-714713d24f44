<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-fmcg</artifactId>
        <groupId>fs-fmcg</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>fs-fmcg-provider</artifactId>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-webpage-customer-api</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-auth-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.appserver</groupId>
            <artifactId>fs-appserver-common-tools</artifactId>
            <version>${appserver-common-tools.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-fcp-biz-server</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-fcp-common</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-text</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.fmcg</groupId>
            <artifactId>fs-fmcg-sdk-ai</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>grpc-context</artifactId>
                    <groupId>io.grpc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>grpc-all</artifactId>
                    <groupId>io.grpc</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fmcg-vision-client</artifactId>
            <version>2.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.fmcg</groupId>
            <artifactId>fs-fmcg-framework-http</artifactId>
            <version>2.2.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-text</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-id-generator</artifactId>
                    <groupId>com.fxiaoke.api</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongo-java-driver</artifactId>
            <version>3.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb.morphia</groupId>
            <artifactId>morphia</artifactId>
            <version>1.3.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mongo-spring-support</artifactId>
            <version>3.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>cglib-nodep</artifactId>
                    <groupId>cglib</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-circuit-breaker</artifactId>
                    <groupId>com.fxiaoke.circuit.breaker</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-configuration</artifactId>
                    <groupId>commons-configuration</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>morphia</artifactId>
                    <groupId>org.mongodb.morphia</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>4.3.21.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>fs-fmcg</groupId>
            <artifactId>fs-fmcg-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>netty-codec-http</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-handler-proxy</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-codec-socks</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fs-fmcg</groupId>
            <artifactId>fs-fmcg-adapter</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-paas-license-common</artifactId>
                    <groupId>com.facishare.paas</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>org.apache.rocketmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>i18n-util</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-enterpriserelation-rest-api2</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fs-fmcg-efficiency</groupId>
            <artifactId>fs-fmcg-efficiency-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-cep-spring-plugin</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>gray-release</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-ai-detector-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mq</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>org.apache.rocketmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>com.alibaba.rocketmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-api</artifactId>
            <version>0.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>protostuff-core</artifactId>
                    <groupId>io.protostuff</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>protostuff-runtime</artifactId>
                    <groupId>io.protostuff</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-enterprise-converter</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>protostuff-core</artifactId>
                    <groupId>io.protostuff</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>protostuff-runtime</artifactId>
                    <groupId>io.protostuff</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.fmcg</groupId>
            <artifactId>fs-fmcg-framework-common</artifactId>
            <version>2.2.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-core</artifactId>
            <version>7.6.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.dubbof</groupId>
            <artifactId>dubbo-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>grpc-stub</artifactId>
                    <groupId>io.grpc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>grpc-netty</artifactId>
                    <groupId>io.grpc</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-rocketmq-support</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <scope>compile</scope>
            <version>1.8</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-license-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-id-generator</artifactId>
                    <groupId>com.fxiaoke.api</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-app-center-api</artifactId>
            <version>1.0.64-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>jedis</artifactId>
                    <groupId>redis.clients</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-beans</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-aop</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-pool</artifactId>
                    <groupId>commons-pool</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>gson</artifactId>
                    <groupId>com.google.code.gson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-web</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-common-storage</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-common-logger</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
        <!--        <exclusion>
                    <artifactId>fs-open-common-result</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>-->
                <exclusion>
                    <artifactId>fs-open-common-utils</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-util</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-client</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.74.Final</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-plat-privilege-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-all</artifactId>
            <version>1.46.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>protobuf-java-util</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>opencensus-api</artifactId>
                    <groupId>io.opencensus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>grpc-stub</artifactId>
                    <groupId>io.grpc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>grpc-protobuf</artifactId>
                    <groupId>io.grpc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>grpc-context</artifactId>
                    <groupId>io.grpc</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-sandbox-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>org.apache.rocketmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>