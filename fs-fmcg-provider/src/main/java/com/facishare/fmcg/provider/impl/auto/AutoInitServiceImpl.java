package com.facishare.fmcg.provider.impl.auto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.config.ConfigService;
import com.facishare.fmcg.adapter.license.LicenseAdapter;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.common.organization.AdminInit;
import com.facishare.fmcg.api.dto.common.organization.AppGray;
import com.facishare.fmcg.api.dto.common.organization.RoleInit;
import com.facishare.fmcg.api.service.common.AppGraService;
import com.facishare.fmcg.api.service.common.RoleInitService;
import com.facishare.fmcg.api.service.rebate.IRebateService;
import com.facishare.fmcg.provider.business.abstraction.*;
import com.facishare.fmcg.provider.business.model.PureDescribeBO;
import com.facishare.fmcg.provider.concurrent.ParallelTaskUtil;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.dao.po.LicensePo;
import com.facishare.fmcg.provider.impl.auth.TPMAuthServiceImpl;
import com.facishare.fmcg.provider.impl.auth.TPMDescribeServiceImpl;
import com.facishare.fmcg.provider.license.AppCodeEnum;
import com.facishare.fmcg.provider.util.LanguageReplaceWrapper;
import com.facishare.fmcg.provider.license.LicenseCodeAppIdEnum;
import com.facishare.webpage.customer.api.model.arg.GetHomePageLayoutByIdArg;
import com.facishare.webpage.customer.api.model.arg.ModifyHomePageLayoutArg;
import com.facishare.webpage.customer.api.model.result.GetHomePageLayoutByIdResult;
import com.facishare.webpage.customer.api.service.HomePageService;
import com.fmcg.framework.http.*;
import com.fmcg.framework.http.contract.data.auth.UpdateEntityOpenness;
import com.fmcg.framework.http.contract.paas.data.PaasFundAccountInit;
import com.fmcg.framework.http.contract.paas.describe.OptionDependence;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeCreateField;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGet;
import com.fmcg.framework.http.contract.paas.layout.PaasAssignRecord;
import com.fmcg.framework.http.contract.paas.layout.PaasCreateLayout;
import com.fmcg.framework.http.contract.paas.layout.PaasEnableEditLayout;
import com.fmcg.framework.http.contract.tpm.ActivityTemplateCopy;
import com.fmcg.framework.http.contract.tpm.CommonScript;
import com.fmcg.framework.http.contract.workflow.DefinitionInitById;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/4/11 下午7:21
 */
@Slf4j
@Component
@SuppressWarnings("Duplicates")
public class AutoInitServiceImpl implements AutoInitService {

    @Resource
    private DescribeBusiness describeBusiness;

    @Resource
    private DataAuthProxy dataAuthProxy;

    @Resource
    private CrmWorkflowProxy crmWorkflowProxy;

    @Resource
    private TPMProxy tpmProxy;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private LicenseDAO licenseDAO;

    @Resource
    private AppGraService appGraService;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private RoleInitService roleInitService;

    @Resource
    private SalesOrderBusiness salesOrderBusiness;

    @Resource
    private TPMActivityMaterialBusiness tpmActivityMaterialBusiness;

    @Resource
    private StoreWriteOffBusiness storeWriteOffBusiness;

    @Resource
    private PaasLayoutProxy paasLayoutProxy;
    @Resource
    private LayOutDescribeBusiness layOutDescribeBusiness;

    @Resource
    private LicenseAdapter licenseAdapter;

    @Resource
    private PaasDescribeProxy paasDescribeProxy;

    @Resource
    private RedissonClient redissonCmd;
    @Resource
    private ConfigService configService;
    @Resource
    private IRebateService rebateService;

    @Resource
    protected HomePageService homePageService;
    @Resource
    private TPMAuthServiceImpl tpmAuthService;
    @Resource
    private TPMDescribeServiceImpl tpmDescribeService;

    private static final String REDIS_UNIQUE_KEY = "auto_tpm_%s";

    private static final String CONFIG_NAME = "gray-rel-fmcg";
    private static final Set<String> NEED_INIT_FLOW_LAYOUT_OBJ_API_NAMES = Sets.newHashSet();

    static {
        NEED_INIT_FLOW_LAYOUT_OBJ_API_NAMES.add("TPMDealerActivityCashingProductObj");
        NEED_INIT_FLOW_LAYOUT_OBJ_API_NAMES.add("TPMDealerActivityCostObj");
    }

    @Override
    public void autoTPM(Integer tenantId) {

        RLock lock = redissonCmd.getLock(String.format(REDIS_UNIQUE_KEY, tenantId));

        try {
            if (lock.tryLock(200L, 300000L, TimeUnit.MILLISECONDS)) {
                log.info("get lock.");
                LicensePo license = licenseDAO.get(tenantId, AppCodeEnum.TPM2.code());
                if (license != null) {
                    log.info("has done.");
                    return;
                }

                if (!licenseAdapter.validate(tenantId, "trade_promotion_management_app")) {
                    log.info("license未生效");
                    return;
                }

                String configMapString = ConfigFactory.getConfig(CONFIG_NAME).get("AUTO_TPM_OBJECT_CONFIG");
                if (Strings.isNullOrEmpty(configMapString)) {
                    return;
                }
                JSONObject config = JSON.parseObject(configMapString);
                JSONArray objectArray = config.getJSONArray("objects");
                if (CollectionUtils.isEmpty(objectArray)) {
                    return;
                }
                //
                Integer templateTenantId = config.getInteger("template_tenant_id");
                //init describe
                LanguageReplaceWrapper.doInChinese(() -> {
                    objectArray.stream().map(JSONObject.class::cast).forEach(object -> {
                        String apiName = object.getString("api_name");
                        log.info("create describe apiName:{}", apiName);
                        List<String> ignoreFields = (List<String>) object.get("ignore_field_list");
                        try {
                            PureDescribeBO pureDescribeBO = describeBusiness.getPureDescribeFromTargetTenant(templateTenantId, apiName, ignoreFields);
                            describeBusiness.createDescribe(tenantId, apiName, pureDescribeBO.getDescribe(), pureDescribeBO.getLayouts(), pureDescribeBO.getListLayout());
                        } catch (Exception e) {
                            log.info("create describe err.apiName:{}, ex:", apiName, e);
                            throw e;
                        }
                        tpmDescribeService.updateIcon(tenantId, apiName);
                    });
                });

                addSalesOrderObjFiled(tenantId);

                addQualityGuaranteePeriod(tenantId);

                addTPMActivityMaterialObjField(tenantId);

                //is_index
                modifyStoreWriteOffActivityType(tenantId);


                ParallelTaskUtil.ParallelTask task = ParallelTaskUtil.createParallelTask();

                //openness
                task.submit(() -> objectOpenness(tenantId, Lists.newArrayList("TPMActivityObj", "TPMActivityItemObj")));
                //workflow
                //init found account and button
                task.submit(() -> {
                    try {
                        initFundAccount(tenantId);
                        PaasDescribeGet.Result activityItemObj = paasDescribeProxy.get(tenantId, -10000, "TPMActivityItemObj");
                        if (activityItemObj.getCode() == 0) {
                            addField(tenantId, "TPMBudgetBusinessSubjectObj", "activity_item_ids");
                        }
                    } catch (Exception e) {
                        log.info("init found e:", e);
                    }
                    LanguageReplaceWrapper.doInChinese(() -> {
                        initButton(tenantId, "init_random_audit_button");
                        initButton(tenantId, "init_store_write_off_button");
                        initButton(tenantId, "init_close_activity_button");
                        initButton(tenantId, "init_activity_proof_button");
                        initButton(tenantId, "init_close_activity_template_button");
                        initButton(tenantId, "init_close_activity_agreement_button");
                    });
                });

                //init role  must init before init layout
                ApiArg<RoleInit.Arg> arg = new ApiArg<>();
                arg.setTenantId(tenantId);
                arg.setTenantAccount(eieaConverter.enterpriseIdToAccount(tenantId));
                arg.setUserId(1000);
                RoleInit.Arg initRoleDataArg = new RoleInit.Arg();
                initRoleDataArg.setSourceTenantId(templateTenantId);
                initRoleDataArg.setTargetTenantId(tenantId);
                arg.setData(initRoleDataArg);
                roleInitService.initRole(arg);
                roleInitService.initRolePermissionFromTemplateTenant(arg);


                //init layout
                objectArray.stream().map(JSONObject.class::cast).forEach(object -> task.submit(() -> {
                    LanguageReplaceWrapper.doInChinese(() -> {
                        String apiName = object.getString("api_name");
                        describeBusiness.reOrderColumnByCopy(templateTenantId, tenantId, apiName);
                        describeBusiness.copyLayoutRule(templateTenantId, tenantId, apiName);
                        describeBusiness.distributeLayoutByCopy(templateTenantId, tenantId, apiName);
                    });
                }));

                //init flow layout
                task.submit(() -> {
                    try {
                        openFlowLayout(tenantId);
                        initFlowLayout(tenantId);
                        initWorkflowById(tenantId, Lists.newArrayList("apprTXMVRK42KU__crmappr"));
                    } catch (Exception ex) {
                        log.info("init flow layout error");
                    }
                });


                //init data
                task.submit(() -> templateCopyData(templateTenantId, tenantId));

                task.submit(() -> {
                    String ea = eieaConverter.enterpriseIdToAccount(tenantId);
                    String templateEa = eieaConverter.enterpriseIdToAccount(templateTenantId);
                    log.info("grayAndInitialization ");
                    templateGrayAndInitialization(tenantId, ea);
                    log.info("updateAppAdmin ");
                    updateAppAdmin(tenantId, ea);
                    copyCustomPage(tenantId, ea, templateTenantId, templateEa);
                });

                task.submit(() -> {
                    //检查返利是否开启，如果开启了，还没有TPM相关的两个字段 需要重刷
                    checkRebate(tenantId);
                });

                task.run();

                //save license

                LicensePo licensePo = toLicensePo(tenantId, -10000, "TENANT." + tenantId, AppCodeEnum.TPM2.code());

                log.info("add license autoTPM, tenantId={}", tenantId);
                licenseDAO.add(licensePo);
            } else {
                log.info("get lock fail");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void addQualityGuaranteePeriod(int tenantId) {
        describeBusiness.addFieldAndCheckFieldIsExist(tenantId, "ProductObj", "quality_guarantee_period", false, true);
    }

    private void checkRebate(Integer tenantId) {
        rebateService.checkRebateFiled(tenantId);
    }

    private boolean init870FlowLayout(Integer tenantId) {
        String configMapString = ConfigFactory.getConfig("variables_fmcg_gray").get("ei_list_fmcg_tpm_gray");
        if (StringUtils.isEmpty(configMapString)) {
            return false;
        }
        String[] tenantIds = configMapString.split(",");
        for (String tenantIdFromFile : tenantIds) {
            if (Objects.equals(tenantId, Integer.parseInt(tenantIdFromFile))) {
                return true;
            }
        }

        return false;
    }

    public void openFlowLayout(Integer tenantId) {
        try {
            Map<String, String> configMap = configService.get("variables_fmcg_gray");
            String eiGrayList = configMap.getOrDefault("ei_flow_layout_gray", "");
            if (org.elasticsearch.common.Strings.isNullOrEmpty(eiGrayList)) {
                throw new RuntimeException("获取灰度流程布局企业失败");
            }
            if (!eiGrayList.contains(String.valueOf(tenantId))) {
                if (eiGrayList.endsWith(",") || org.elasticsearch.common.Strings.isNullOrEmpty(eiGrayList)) {
                    eiGrayList += "\"" + tenantId + "\"";
                } else {
                    eiGrayList += ",\"" + tenantId + "\"";
                }
                configService.update("variables_fmcg_gray", "ei_flow_layout_gray", eiGrayList);
            }

        } catch (Exception e) {
            log.error("updateFlowLayoutConfig is error tenantId:{}", tenantId, e);
        }

    }

    public void initFlowLayout(Integer tenantId) {
        for (String needInitFlowLayoutObjApiName : NEED_INIT_FLOW_LAYOUT_OBJ_API_NAMES) {
            try {
                PaasCreateLayout.Arg arg = new PaasCreateLayout.Arg();
                arg.setLayoutData(JSON.parseObject(layout(String.format("%sFlow", needInitFlowLayoutObjApiName))));
                PaasCreateLayout.Result result = layOutDescribeBusiness.createLayout(tenantId, arg);
                if (result.getCode() != 0) {
                    log.error("create layout error,apiName:{}", needInitFlowLayoutObjApiName);
                }
            } catch (Exception ex) {
                log.error("apiName:{} init flow layout error", needInitFlowLayoutObjApiName);
            }
        }

    }


    @Override
    public void createExpenseClaimObj(Integer tenantId) {
        LanguageReplaceWrapper.doInChinese(() -> {
            String[] apiNames = {"ExpenseClaimFormObj", "ExpenseClaimFormDetailObj"};

            String baseDir = this.getClass().getClassLoader().getResource("expense/").getPath();
            for (String apiName : apiNames) {
                describeBusiness.createDescribe(tenantId, apiName, getJSONListFromFile(baseDir, apiName + "\\.json").get(0), getJSONListFromFile(baseDir, apiName + ".*DetailLayout\\.json"), null);
            }
            Map<String, String> detailRecordLayoutMap = getExpenseDetailRecordLayoutMap();
            assignRecordType(tenantId, apiNames[0], "00000000000000000000000000000006", "default__c", Lists.newArrayList("default__c", "record_bD1Pl__c", "record_kYs9j__c", "record_Lu2Cc__c", "record_Ix1qT__c", "record_wGbbj__c", "record_kpmx0__c"));
            assignRecordType(tenantId, apiNames[1], "00000000000000000000000000000006", "default__c", new ArrayList<>(detailRecordLayoutMap.keySet()));
            describeBusiness.modifyLayoutList(tenantId, apiNames[1], getExpenseMaster2DetailRecordMap());
            describeBusiness.assignLayoutAndRecordByMap(tenantId, apiNames[1], getExpenseDetailRecordLayoutMap());

            removeMenuFromCrmDefault(tenantId, "ExpenseClaimFormDetailObj");


            for (String apiName : apiNames) {
                PaasEnableEditLayout.Arg arg = new PaasEnableEditLayout.Arg();
                arg.setDescribeApiName(apiName);
                PaasEnableEditLayout.Result enableEditResult = paasLayoutProxy.enableEditLayout(tenantId, -10000, arg);
                if (enableEditResult.getCode() != 0) {
                    log.info("enableEditLayout:{}", enableEditResult);
                }
            }

            OptionDependence.Arg optionDependenceArg = JSON.parseObject("{\"fieldDependence\":{\"describeApiName\":\"ExpenseClaimFormDetailObj\",\"fieldApiName\":\"record_type\",\"childFieldName\":\"type\",\"dependence\":[{\"value\":\"default__c\",\"childOptions\":[\"5\",\"6\",\"7\",\"other\"]},{\"value\":\"record_P1mpZ__c\",\"childOptions\":[\"1\",\"2\",\"3\",\"4\",\"other\"]},{\"value\":\"record_Ak61I__c\",\"childOptions\":[\"15\",\"16\",\"17\",\"18\",\"other\"]},{\"value\":\"record_0fFNl__c\",\"childOptions\":[\"19\",\"20\",\"21\",\"22\",\"23\",\"other\"]},{\"value\":\"record_Eex1M__c\",\"childOptions\":[\"8\",\"9\",\"10\",\"11\",\"12\",\"13\",\"14\",\"other\"]},{\"value\":\"record_oT4K1__c\",\"childOptions\":[\"24\",\"25\",\"26\",\"27\",\"other\"]}]}}", OptionDependence.Arg.class);
            OptionDependence.Result optionDependenceResult = paasDescribeProxy.createOptionDependence(tenantId, -10000, optionDependenceArg);
            if (optionDependenceResult.getCode() != 0) {
                log.info("dependence:{}", optionDependenceResult);
            }
        });
    }

    private void removeMenuFromCrmDefault(Integer tenantId, String expenseClaimFormDetailObj) {

    }

    private Map<String, String> getExpenseDetailRecordLayoutMap() {
        Map<String, String> map = new HashMap<>();
        map.put("default__c", "layout_JW070__c");
        map.put("record_P1mpZ__c", "layout_2U8On__c");
        map.put("record_Ak61I__c", "layout_17i9y__c");
        map.put("record_0fFNl__c", "layout_3f2im__c");
        map.put("record_oT4K1__c", "layout_VavgI__c");
        map.put("record_Eex1M__c", "layout_gi01Q__c");
        return map;
    }

    private Map<String, List<String>> getExpenseMaster2DetailRecordMap() {
        Map<String, List<String>> map = new HashMap<>();
        map.put("default__c", Lists.newArrayList("record_P1mpZ__c"));
        map.put("record_kYs9j__c", Lists.newArrayList("default__c"));
        map.put("record_Lu2Cc__c", Lists.newArrayList("record_Eex1M__c"));
        map.put("record_bD1Pl__c", Lists.newArrayList("record_Ak61I__c"));
        map.put("record_Ix1qT__c", Lists.newArrayList("record_0fFNl__c"));
        map.put("record_wGbbj__c", Lists.newArrayList("record_oT4K1__c"));
        map.put("record_kpmx0__c", Lists.newArrayList("default__c", "record_P1mpZ__c", "record_Ak61I__c", "record_0fFNl__c", "record_Eex1M__c", "record_oT4K1__c"));
        return map;
    }

    private void assignRecordType(int tenantId, String apiName, String roleCode, String defaultRecordType, List<String> recordList) {
        PaasAssignRecord.Arg arg = new PaasAssignRecord.Arg();
        arg.setDescribeApiName(apiName);
        PaasAssignRecord.Role role = new PaasAssignRecord.Role();
        role.setRecords(recordList);
        role.setRoleCode(roleCode);
        role.setDefaultRecord(defaultRecordType);
        arg.setRoleList(JSON.toJSONString(Lists.newArrayList(role)));
        PaasAssignRecord.Result result = paasLayoutProxy.assignRecord(tenantId, -10000, arg);
        if (result.getErrCode() != 0) {
            log.info("assign err. arg:{},result:{}", arg, result);
        }
    }


    private List<JSONObject> getJSONListFromFile(String dirPath, String namePattern) {
        File dir = new File(dirPath);
        List<JSONObject> result = new ArrayList<>();
        for (File file : dir.listFiles()) {
            if (file.getName().matches(namePattern)) {
                try {
                    result.add(JSON.parseObject(new String(Files.readAllBytes(file.toPath()))));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return result;
    }


    private void modifyStoreWriteOffActivityType(Integer tenantId) {
        storeWriteOffBusiness.modifyActivityType(tenantId);
    }

    private void addSalesOrderObjFiled(Integer tenantId) {
        salesOrderBusiness.addSalesOrderTPMField(null, tenantId);
    }

    private void addTPMActivityMaterialObjField(Integer tenantId) {
        tpmActivityMaterialBusiness.addTPMActivityMaterialObjField(tenantId);
    }

    private void templateCopyData(Integer templateTenantId, Integer tenantId) {
        log.info(" init templateCopyData arg: templateTenantId ={}, tenantId={}", templateTenantId, tenantId);
        ActivityTemplateCopy.Arg arg = new ActivityTemplateCopy.Arg();
        arg.setSourceEI(templateTenantId);
        arg.setTargetEI(tenantId);
        ActivityTemplateCopy.Result result = tpmProxy.templateCopy(tenantId, -10000, arg);
        log.info(" init templateCopyData result={}", JSON.toJSONString(result));
    }

    private void templateGrayAndInitialization(int tenantId, String tenantAccount) {
        ApiArg<AppGray.Arg> arg = new ApiArg<>();
        arg.setTenantId(tenantId);
        arg.setTenantAccount(tenantAccount);
        arg.setUserId(1000);
        arg.setData(new AppGray.Arg());
        appGraService.addGrayAndInitializationTemplate(arg);
    }

    private void updateAppAdmin(int tenantId, String tenantAccount) {
        ApiArg<AdminInit.Arg> arg = new ApiArg<>();
        arg.setTenantId(tenantId);
        arg.setTenantAccount(tenantAccount);
        arg.setUserId(1000);
        arg.setData(new AdminInit.Arg());
        roleInitService.preAdminTemplate(arg);
    }

    private void objectOpenness(Integer tenantId, List<String> apiNames) {

        UpdateEntityOpenness.Arg arg = new UpdateEntityOpenness.Arg();
        UpdateEntityOpenness.Context context = new UpdateEntityOpenness.Context();
        arg.setEntityOpenness(Lists.newArrayList());
        context.setAppId("CRM");
        context.setTenantId(tenantId);
        context.setUserId(-10000);
        arg.setContext(context);
        apiNames.forEach(name -> {
            UpdateEntityOpenness.Entity entity = new UpdateEntityOpenness.Entity();
            entity.setApiName(name);
            entity.setAppId("CRM");
            entity.setPermission(1);
            entity.setScope(0);
            entity.setTenantId(tenantId);
            arg.getEntityOpenness().add(entity);
        });

        UpdateEntityOpenness.Result result = dataAuthProxy.updateEntityOpenness(tenantId, -10000, arg);
        if (result.getErrCode() != 0) {
            log.info("set object openness fail.arg:{},result:{}", arg, result);
        }
    }

    public void initWorkflowById(Integer tenantId, List<String> flowIds) {
        DefinitionInitById.Arg arg = new DefinitionInitById.Arg();
        arg.setSourceWorkflowIds(flowIds);
        DefinitionInitById.Result result = crmWorkflowProxy.definitionBySourceWorkflowId(tenantId, -10000, arg);
        if (result.getCode() != 0) {
            log.error("init workflow by id err. rst:{},id:{}", result, JSON.toJSONString(flowIds));
        }
    }

    private void initFundAccount(int tenantId) {
        PaasFundAccountInit.Arg arg = new PaasFundAccountInit.Arg();
        arg.setObjectApiNames(Lists.newArrayList("TPMDealerActivityCostObj"));
        PaasFundAccountInit.Result result = paasDataProxy.initFoundAccount(tenantId, -10000, arg);
        log.info("init fund account info:{}", result);
    }

    private void initButton(Integer tenantId, String actionCode) {
        CommonScript.Arg arg = new CommonScript.Arg();
        arg.setModule(actionCode);
        arg.setTenantIds(Lists.newArrayList(String.valueOf(tenantId)));
        CommonScript.Result result = tpmProxy.commonScript(tenantId, -10000, arg);
        log.info("init activity arg:{},rst:{}", arg, result);
    }


    private LicensePo toLicensePo(int tenantId, Integer employeeId, String owner, String appCode) {
        LicensePo po = new LicensePo();

        po.setTenantId(tenantId);
        po.setOwner(owner);
        po.setAppCode(appCode);
        po.setCode(UUID.randomUUID().toString());
        po.setCreator(employeeId);
        po.setCreateTime(System.currentTimeMillis());
        po.setLastUpdater(0);
        po.setLastUpdateTime(0L);
        po.setDeleted(false);
        po.setDeleteTime(0L);

        return po;
    }


    private void addField(Integer tenantId, String describeApiName, String fieldApiName) throws IOException {
        PaasDescribeCreateField.Arg arg = new PaasDescribeCreateField.Arg();
        arg.setDescribeAPIName(describeApiName);
        arg.setFieldDescribe(readFile("field/" + fieldApiName + ".json"));
        PaasDescribeCreateField.Result result = paasDescribeProxy.createField(tenantId, -10000, arg);
        log.info("add result:{}", JSON.toJSONString(result));
    }

    private String readFile(String fileName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile("classpath:budget2/" + fileName);
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    private String layout(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:tpm2/%sDetailLayout.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    public void copyCustomPage(int tenantId, String ea, int templateTenantId, String templateEa) {

        log.info("copyCustomPage is start tenantId:{}, templateTenantId:{}", tenantId, templateTenantId);
        String tpmCopyCustomPageApiNames = ConfigFactory.getConfig(CONFIG_NAME).get("tpmCopyCustomPageApiNames");

        if (!StringUtils.isEmpty(tpmCopyCustomPageApiNames)) {
            Lists.newArrayList(tpmCopyCustomPageApiNames.split(",")).forEach(layoutApiName -> {
                try {
                    copyCustomPage(templateEa, templateTenantId, ea, tenantId, layoutApiName);
                } catch (Exception e) {
                    log.error("copyCustomPage copyAppPageById is error ea is {}", ea, e);
                }
            });
        }
    }

    public void copyCustomPage(String sourceEa, int sourceEid, String ea, int eid, String layoutApiName) {
        GetHomePageLayoutByIdArg getHomePageLayoutByIdArg = new GetHomePageLayoutByIdArg();
        getHomePageLayoutByIdArg.setAppType(4);
        getHomePageLayoutByIdArg.setEnterpriseId(sourceEid);
        getHomePageLayoutByIdArg.setEnterpriseAccount(sourceEa);
        getHomePageLayoutByIdArg.setEmployeeId(-10000);
        getHomePageLayoutByIdArg.setLayoutApiName(layoutApiName);
        getHomePageLayoutByIdArg.setType(ClientTypeEnum.Web);
        getHomePageLayoutByIdArg.setLocale(Locale.CHINESE);
        GetHomePageLayoutByIdResult getHomePageLayoutByIdResult = homePageService.getHomePageLayoutById(getHomePageLayoutByIdArg);
        if (Objects.nonNull(getHomePageLayoutByIdResult) && Objects.nonNull(getHomePageLayoutByIdResult.getHomePageLayout())) {
            ModifyHomePageLayoutArg modifyHomePageLayoutArg = new ModifyHomePageLayoutArg();
            modifyHomePageLayoutArg.setAppId("PortalPage");
            modifyHomePageLayoutArg.setApplyType(0);
            modifyHomePageLayoutArg.setAppType(4);
            modifyHomePageLayoutArg.setEmployeeId(-10000);
            modifyHomePageLayoutArg.setEnterpriseAccount(ea);
            modifyHomePageLayoutArg.setEnterpriseId(eid);
            if (Objects.nonNull(getHomePageLayoutByIdResult.getHomePageLayout().getCustomerLayout()) && Objects.nonNull(getHomePageLayoutByIdResult.getHomePageLayout().getCustomerLayout().getJSONObject("components"))) {
                JSONArray jsonArray = new JSONArray();
                for (Map.Entry<String, Object> entry : getHomePageLayoutByIdResult.getHomePageLayout().getCustomerLayout().getJSONObject("components").entrySet()) {
                    if (Objects.nonNull(((JSONObject) entry.getValue()).getJSONObject("props"))) {
                        ((JSONObject) entry.getValue()).getJSONObject("props").forEach((k, v) -> {
                            if (Objects.nonNull(v)) {
                                ((JSONObject) entry.getValue()).put(k, v);
                            }
                        });
                        ((JSONObject) entry.getValue()).remove("props");
                    }
                    jsonArray.add(entry.getValue());
                }
                getHomePageLayoutByIdResult.getHomePageLayout().getCustomerLayout().put("components", jsonArray);
            }
            modifyHomePageLayoutArg.setHomePageLayout(getHomePageLayoutByIdResult.getHomePageLayout());
            homePageService.modifyHomePageLayout(modifyHomePageLayoutArg);
        } else {
            log.info("copyCustomPage ea:{} layoutApiName:{} template is not config", ea, layoutApiName);
        }
    }

    public void autoCopyCustomPage(int tenantId) {
        String configMapString = ConfigFactory.getConfig(CONFIG_NAME).get("AUTO_TPM_OBJECT_CONFIG");
        if (Strings.isNullOrEmpty(configMapString)) {
            return;
        }
        JSONObject config = JSON.parseObject(configMapString);
        Integer templateTenantId = config.getInteger("template_tenant_id");
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        String templateEa = eieaConverter.enterpriseIdToAccount(templateTenantId);
        copyCustomPage(tenantId, ea, templateTenantId, templateEa);
    }

    @Override
    public void autoTPM(Integer tenantId, boolean isTPMLimitCode) {
        List<LicensePo> pos = licenseDAO.query(tenantId, Lists.newArrayList(AppCodeEnum.TPM2.code(), AppCodeEnum.TPM.code()));
        if (CollectionUtils.isEmpty(pos)) {
            log.info("init license limit done.");
            autoTPM(tenantId);
        }

        if (!isTPMLimitCode) {
            return;
        }

        try {
            tpmAuthService.createRole(String.valueOf(tenantId), "营销活动应用许可", LicenseCodeAppIdEnum.TPM_LIMIT.getCode(), LicenseCodeAppIdEnum.TPM_LIMIT.getAccessRole());
        } catch (Exception e) {
            log.info("init tpm limit createRole failed.", e);
        }

        if (CollectionUtils.isNotEmpty(pos)) {
            boolean existTPM2 = pos.stream().anyMatch(po -> AppCodeEnum.TPM2.code().equals(po.getAppCode()));
            if (existTPM2) {
                log.info("init tpm limit done.");
                roleInitService.batchAddAccessRoleUser(tenantId, LicenseCodeAppIdEnum.TPM_LIMIT.getCode());
            }
        }


        LicensePo licenseLimit = licenseDAO.get(tenantId, AppCodeEnum.TPM_LIMIT.code());
        if (Objects.isNull(licenseLimit)) {
            LicensePo licensePo = toLicensePo(tenantId, -10000, "TENANT." + tenantId, AppCodeEnum.TPM_LIMIT.code());
            log.info("add license limit autoTPM, tenantId={}", tenantId);
            licenseDAO.add(licensePo);
        }
    }
}
