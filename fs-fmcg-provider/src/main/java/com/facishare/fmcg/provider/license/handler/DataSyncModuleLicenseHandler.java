package com.facishare.fmcg.provider.license.handler;

import com.facishare.fmcg.provider.license.ModuleLicenseHandlerBase;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * <p>
 * AI堆头费用审核
 */
@SuppressWarnings("Duplicates")
@Component("dataSyncModuleLicenseHandler")
public class DataSyncModuleLicenseHandler extends ModuleLicenseHandlerBase {

    @Override
    public boolean validate(int tenantId, String tenantAccount, int userId) {
        return true;
    }

    @Override
    public String formatOwnerCode(int tenantId, String tenantAccount, int userId) {
        return "TENANT." + tenantId;
    }

    @Override
    public String getAppCode() {
        return "FMCG.DATA_SYNC";
    }
}
