package com.facishare.fmcg.provider.impl.ai.config;

import com.facishare.fmcg.adapter.ai.AIServiceAdapter;
import com.facishare.fmcg.adapter.organization.OrganizationAdapter;
import com.facishare.fmcg.adapter.role.RoleService;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.config.AIConfigVO;
import com.facishare.fmcg.api.dto.ai.config.AddSwitch;
import com.facishare.fmcg.api.dto.ai.config.AdjustEmployee;
import com.facishare.fmcg.api.dto.ai.config.EnableSwitch;
import com.facishare.fmcg.api.dto.ai.config.NameVO;
import com.facishare.fmcg.api.dto.ai.config.QueryUsageAndConfig;
import com.facishare.fmcg.api.service.ai.config.AISwitchService;
import com.facishare.fmcg.provider.abstraction.ServiceBase;
import com.facishare.fmcg.provider.dao.abstraction.AISwitchDAO;
import com.facishare.fmcg.provider.dao.abstraction.AccountDAO;
import com.facishare.fmcg.provider.dao.abstraction.BizCallNumberDAO;
import com.facishare.fmcg.provider.dao.po.AISwitchPO;
import com.facishare.fmcg.provider.dao.po.AccountPO;
import com.facishare.fmcg.provider.dao.po.BizCallNumberPO;
import com.facishare.organization.adapter.api.model.biz.department.Department;

import java.util.ArrayList;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/6/1 下午2:15
 */
@Service(value = "aiSwitchService")
public class AISwitchServiceImpl extends ServiceBase implements AISwitchService {

    @Resource
    private AISwitchDAO aiSwitchDAO;

    @Resource
    private AIServiceAdapter aiServiceAdapter;

    @Resource
    private OrganizationAdapter organizationAdapter;

    @Resource
    private RoleService roleService;

    @Resource
    private AccountDAO accountDAO;

    @Resource
    private BizCallNumberDAO bizCallNumberDAO;

    private Logger log = LoggerFactory.getLogger(AISwitchServiceImpl.class);


    @Override
    public ApiResult<AdjustEmployee.Result> adjustEmployee(ApiArg<AdjustEmployee.Arg> arg) {
        log.info("[ai_pile_switch] add Employee,arg:{}",arg);
        arg.getData().setEmployeeIds(arg.getData().getEmployeeIds()==null?new ArrayList<>():arg.getData().getEmployeeIds());
        arg.getData().setDepartmentIds(arg.getData().getDepartmentIds()==null?new ArrayList<>():arg.getData().getDepartmentIds());
        arg.getData().setRoleIds(arg.getData().getRoleIds()==null?new ArrayList<>():arg.getData().getRoleIds());
        aiSwitchDAO.updateEffectiveEmployee(arg.getData().getId(),arg.getData().getEmployeeIds(),arg.getData().getDepartmentIds(),arg.getData().getRoleIds());
        AdjustEmployee.Result rst = new AdjustEmployee.Result();
        log.info("[ai_pile_switch] add Employee,arg:{},rst:{}",arg,rst);
        return setSuccess(rst);
    }

    @Override
    public ApiResult<EnableSwitch.Result> enableSwitch(ApiArg<EnableSwitch.Arg> arg) {
        log.info("[ai_pile_switch] enableSwitch,arg:{}",arg);
        aiSwitchDAO.adjustSwitch(arg.getData().getId(),arg.getData().isEnable());
        return setSuccess(new EnableSwitch.Result());
    }

    @Override
    public ApiResult<QueryUsageAndConfig.Result> queryUsageAndConfig(ApiArg<QueryUsageAndConfig.Arg> arg) {
        log.info("[ai_pile_switch] queryUsageAndConfig,arg:{}",arg);
        arg.getData().setType(arg.getData().getType()==null?"PILE_DETECT":arg.getData().getType());
        AISwitchPO po = aiSwitchDAO.query(arg.getTenantId(),arg.getData().getType());
        if(po==null){
            return new ApiResult<>();
           /* po = new AISwitchPO();
            po.setModelId("18dcb71a-46e6-4fb9-8ec2-f1b4d872d076");
            po.setEnable(false);
            po.setTenantId(arg.getTenantId());
            po.setEffectiveEmployees(new ArrayList<>());
            po.setEmployees(new ArrayList<>());
            po.setDepartments(new ArrayList<>());
            po.setRoles(new ArrayList<>());
            po.setType(arg.getData().getType());
            aiSwitchDAO.insert(po);*/
        }
        po.setEmployees(po.getEmployees()==null?new ArrayList<>():po.getEmployees());
        po.setDepartments(po.getDepartments()==null?new ArrayList<>():po.getDepartments());
        po.setRoles(po.getRoles()==null?new ArrayList<>():po.getRoles());
        AIConfigVO vo = new AIConfigVO();
        vo.setEnable(po.getEnable());
        vo.setEmployees(new ArrayList<>());
        vo.setDepartments(new ArrayList<>());
        vo.setRoles(new ArrayList<>());
        organizationAdapter.queryEmployees(po.getTenantId(),po.getEmployees()).forEach(v->vo.getEmployees().add(new NameVO(String.valueOf(v.getEmployeeId()),v.getName())));
        po.getDepartments().forEach(id->{
            Department department = organizationAdapter.getDepartment(arg.getTenantId(),id);
            vo.getDepartments().add(new NameVO(String.valueOf(department.getDepartmentId()),department.getName()));
        });
        Map<String,String> idToName = roleService.queryRoleNames(arg.getTenantId());
        po.getRoles().forEach(v->{
            vo.getRoles().add(new NameVO(v,idToName.get(v)));
        });
        vo.setId(po.getId().toString());
        AccountPO accountPO = accountDAO.query(arg.getTenantId()).get(0);
        vo.setBalance(Math.round(accountPO.getBalance()*100)/100.0);
        BizCallNumberPO bizCallNumberPO = bizCallNumberDAO.query(arg.getTenantId(),"PILE_DETECT");
        vo.setDetectedNum(bizCallNumberPO.getSuccess());
        vo.setUsedExpenses(Math.round(accountPO.getDeduction()*100)/100.0);
        QueryUsageAndConfig.Result rst = new QueryUsageAndConfig.Result();
        rst.setConfig(vo);
        log.info("[ai_pile_switch] queryUsageAndConfig,arg:{},rst:{}",arg,rst);
        return setSuccess(rst);
    }

    @Override
    public ApiResult<AddSwitch.Result> addSwitch(ApiArg<AddSwitch.Arg> arg) {
        log.info("[ai_pile_switch] addSwitch,arg:{}",arg);
        AISwitchPO po = new AISwitchPO();
        po.setModelId(arg.getData().getModelId());
        po.setEnable(arg.getData().getEnable());
        po.setTenantId(arg.getData().getTenantId());
        po.setEffectiveEmployees(arg.getData().getEffectiveEmployees()==null?new ArrayList<>():arg.getData().getEffectiveEmployees());
        po.setEmployees(arg.getData().getEmployees()==null?new ArrayList<>():arg.getData().getEmployees());
        po.setDepartments(arg.getData().getDepartments()==null?new ArrayList<>():arg.getData().getDepartments());
        po.setRoles(arg.getData().getRoles()==null?new ArrayList<>():arg.getData().getRoles());
        po.setType(arg.getData().getType()==null?"PILE_DETECT":arg.getData().getType());
        aiSwitchDAO.insert(po);
        AddSwitch.Result result = new AddSwitch.Result();
        result.setId(po.getId().toString());
        log.info("[ai_pile_switch] addSwitch,rst:{}",result);
        return setSuccess(result);
    }
}
