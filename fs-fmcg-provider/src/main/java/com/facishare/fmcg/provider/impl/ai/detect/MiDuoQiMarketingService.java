package com.facishare.fmcg.provider.impl.ai.detect;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.detect.MiDuoQiMarketingDetect;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.ai.detect.IMiDuoQiMarketingService;
import com.facishare.fmcg.provider.abstraction.ServiceBase;
import com.facishare.fmcg.vision.client.MiDuoQiMarketingDetectClient;
import com.facishare.fmcg.vision.client.errors.VisionClientException;
import com.facishare.fmcg.vision.client.model.MiDuoQiMarketingDetectRequest;
import com.facishare.fmcg.vision.client.model.MiDuoQiMarketingDetectResponse;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataFindById;
import com.fmcg.framework.http.contract.paas.data.PaasDataIncrementUpdate;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class MiDuoQiMarketingService extends ServiceBase implements IMiDuoQiMarketingService {

    @Resource
    private MiDuoQiMarketingDetectClient miDuoQiMarketingDetectClient;
    @Resource
    private PaasDataProxy paasDataProxy;

    @Override
    public ApiResult<MiDuoQiMarketingDetect.Result> detect(ApiArg<MiDuoQiMarketingDetect.Arg> arg) {
        JSONObject data = findDataById(arg.getTenantId(), arg.getData().getId());
        if (Objects.isNull(data)) {
            return setFailure(500, "data not found.");
        }

        if (!data.containsKey("mdq_zhanmai01__c")) {
            return setFailure(500, "image not found.");
        }

        JSONArray images = data.getJSONArray("mdq_zhanmai01__c");
        if (images.isEmpty()) {
            return setFailure(500, "image not found.");
        }

        List<String> paths = Lists.newArrayList();
        for (int i = 0; i < images.size(); i++) {
            JSONObject image = images.getJSONObject(i);
            paths.add(image.getString("path"));
        }

        if (paths.isEmpty()) {
            return setFailure(500, "image not found.");
        }

        MiDuoQiMarketingDetectRequest request = new MiDuoQiMarketingDetectRequest();
        request.setPaths(paths);
        MiDuoQiMarketingDetectResponse response;
        try {
            response = miDuoQiMarketingDetectClient.detect(String.valueOf(arg.getTenantId()), request);
        } catch (VisionClientException ex) {
            return setFailure(500505, ex.getErrorMessage());
        } catch (Exception ex) {
            log.error("detect error : ", ex);
            return setFailure(500500, "detect cause unknown exception.");
        }

        int count = response.getCount();
        String level;
        if (count <= 2) {
            level = "l0";
        } else if (count == 3) {
            level = "l1";
        } else if (count == 4) {
            level = "l2";
        } else if (count == 5) {
            level = "l3";
        } else {
            level = "l3";
        }

        update(arg.getTenantId(), arg.getData().getId(), level, response.getExplain(), response.getSuggestion());

        return setSuccess(MiDuoQiMarketingDetect.Result.builder().id(arg.getData().getId()).build());
    }

    private void update(int tenantId, String id, String level, String comment, String suggestion) {
        PaasDataIncrementUpdate.Arg arg = new PaasDataIncrementUpdate.Arg();
        arg.setData(Maps.newHashMap());

        arg.getData().put("_id", id);
        arg.getData().put("zm_audit_level__c", level);
        arg.getData().put("zm_audit_comment__c", comment);
        arg.getData().put("zm_audit_suggestion__c", suggestion);

        PaasDataIncrementUpdate.Result result = paasDataProxy.incrementUpdate(tenantId, -10000, "CheckinsObj", false, arg);

        if (Objects.nonNull(result.getErrCode()) && result.getErrCode() != 0) {
            throw new FmcgException("increment update failed : " + result.getErrMessage(), 500);
        }
    }

    private JSONObject findDataById(int tenantId, String id) {
        PaasDataFindById.Arg arg = new PaasDataFindById.Arg();

        arg.setDescribeApiName("CheckinsObj");
        arg.setDataId(id);
        arg.setCalculateCount(false);
        arg.setCalculateFormula(false);
        arg.setCalculateQuote(false);
        arg.setFillExtendInfo(false);
        arg.setIncludeInvalid(false);
        arg.setIncludeRelevantTeam(false);
        arg.setSelectFields(Lists.newArrayList("mdq_zhanmai01__c", "_id", "name"));

        PaasDataFindById.Result result = paasDataProxy.findById(tenantId, -10000, arg);
        if (Objects.nonNull(result.getCode()) && result.getCode() != 0) {
            throw new FmcgException("object data not found : " + result.getMessage(), 500);
        }

        log.info("find by id result result {}", JSON.toJSONString(result));

        return result.getData().getObjectData();
    }
}
