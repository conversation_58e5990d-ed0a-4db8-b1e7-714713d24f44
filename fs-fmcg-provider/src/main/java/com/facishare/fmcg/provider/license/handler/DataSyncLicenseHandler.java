package com.facishare.fmcg.provider.license.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.facishare.fcp.utils.CollectionUtils;
import com.facishare.fmcg.adapter.exception.AdapterException;
import com.facishare.fmcg.adapter.interconnection.EnterpriseRelationAdapter;
import com.facishare.fmcg.adapter.interconnection.model.GetUpstreamAndDownstreamEIList;
import com.facishare.fmcg.adapter.metadata.DataAdapter;
import com.facishare.fmcg.adapter.metadata.DescribeAdapter;
import com.facishare.fmcg.adapter.metadata.FieldDescribeAdapter;
import com.facishare.fmcg.adapter.metadata.RecordTypeService;
import com.facishare.fmcg.adapter.metadata.dto.data.QueryLayout;
import com.facishare.fmcg.adapter.metadata.dto.describe.Create;
import com.facishare.fmcg.adapter.metadata.dto.field.Add;
import com.facishare.fmcg.adapter.metadata.dto.recordType.RecordType;
import com.facishare.fmcg.adapter.role.RoleService;
import com.facishare.fmcg.provider.license.ModuleLicenseHandlerBase;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@SuppressWarnings("Duplicates")
@Component("dataSyncLicenseHandler")
public class DataSyncLicenseHandler extends ModuleLicenseHandlerBase {

    private static final String[] API_NAMES = new String[20];
    private static final String[] UPGRADE_API_NAMES = new String[2];
    private static final Set<String> SYSTEM_FIELDS = new HashSet<>();
    private static final String ACCOUNT_OBJ_API_NAME = "AccountObj";
    private static final String DEALER_RECORD_API_NAME = "record_dealer__c";
    private static final String DEALER_RECORD_DISPLAY_NAME = "经销商";
    private static final String DEALER_DEFAULT_LAYOUT_API_NAME = "AccountObj_layout_generate_by_UDObjectServer__c";
    private static final String DESCRIBE_CONFIG_ROOT = "fs-fmcg-dds-object-describe";
    private static final Logger logger = LoggerFactory.getLogger(DataSyncLicenseHandler.class);

    static {
        UPGRADE_API_NAMES[0] = "DealerOutboundDeliveryNoteObj";
        UPGRADE_API_NAMES[1] = "DealerOutboundDeliveryNoteProductObj";

        API_NAMES[0] = "DealerOutboundDeliveryNoteObj";
        API_NAMES[1] = "DealerOutboundDeliveryNoteProductObj";

        API_NAMES[2] = "DealerOrderObj";
        API_NAMES[3] = "DealerOrderProductObj";

        API_NAMES[4] = "DealerDeliveryNoteObj";
        API_NAMES[5] = "DealerDeliveryNoteProductObj";

        API_NAMES[6] = "DealerStockObj";

        API_NAMES[7] = "StoreReceivedNoteObj";
        API_NAMES[8] = "StoreReceivedNoteProductObj";

        API_NAMES[9] = "DealerReturnOrderObj";
        API_NAMES[10] = "DealerReturnOrderProductObj";

        API_NAMES[11] = "StoreStockObj";

        API_NAMES[12] = "StoreSalesVolumeObj";
        API_NAMES[13] = "StoreSalesVolumeProductObj";

        API_NAMES[14] = "DealerGoodsReceivedNoteObj";
        API_NAMES[15] = "DealerGoodsReceivedNoteProductObj";

        API_NAMES[16] = "DealerReturnNoticeNoteObj";
        API_NAMES[17] = "DealerReturnNoticeNoteProductObj";

        API_NAMES[18] = "DealerCheckinsObj";
        API_NAMES[19] = "DealerPointsRewardDetailObj";

        SYSTEM_FIELDS.add("tenant_id");
        SYSTEM_FIELDS.add("name");
        SYSTEM_FIELDS.add("owner");
        SYSTEM_FIELDS.add("lock_status");
        SYSTEM_FIELDS.add("life_status");
        SYSTEM_FIELDS.add("record_type");
        SYSTEM_FIELDS.add("created_by");
        SYSTEM_FIELDS.add("create_time");
        SYSTEM_FIELDS.add("last_modified_by");
        SYSTEM_FIELDS.add("last_modified_time");
        SYSTEM_FIELDS.add("extend_obj_data_id");
        SYSTEM_FIELDS.add("package");
        SYSTEM_FIELDS.add("object_describe_id");
        SYSTEM_FIELDS.add("object_describe_api_name");
        SYSTEM_FIELDS.add("version");
        SYSTEM_FIELDS.add("lock_user");
        SYSTEM_FIELDS.add("lock_rule");
        SYSTEM_FIELDS.add("life_status_before_invalid");
        SYSTEM_FIELDS.add("is_deleted");
        SYSTEM_FIELDS.add("out_tenant_id");
        SYSTEM_FIELDS.add("out_owner");
        SYSTEM_FIELDS.add("data_own_department");
        SYSTEM_FIELDS.add("active_status");
        SYSTEM_FIELDS.add("relevant_team");
        SYSTEM_FIELDS.add("_id");
        SYSTEM_FIELDS.add("owner_department");
    }

    @Resource
    private DescribeAdapter describeAdapter;
    @Resource
    private FieldDescribeAdapter fieldDescribeAdapter;
    @Resource
    private RecordTypeService recordTypeService;
    @Resource
    private RoleService roleService;
    @Resource
    private DataAdapter dataAdapter;
    @Resource
    private EnterpriseRelationAdapter enterpriseRelationAdapter;

    /**
     * active FMCG.DDS license
     */
    @Override
    public void active(int tenantId, String tenantAccount, int userId) {

        try {
            fieldDescribeAdapter.create(tenantId, buildFieldArg("AccountObj", "AccountObj.superior_customer_id__c"));

        } catch (AdapterException ex) {
            logger.error("DDS package active error on using metadata adapter when create field.", ex);
        }
        for (String apiName : API_NAMES) {
            try {
                Create.Arg arg = buildDescribeArg(apiName);
                if (arg != null) {
                    describeAdapter.create(tenantId, arg);
                }
            } catch (AdapterException ex) {
                logger.error("DDS package active error on using metadata adapter when create object.", ex);
            }
        }
        GetUpstreamAndDownstreamEIList.Arg arg = new GetUpstreamAndDownstreamEIList.Arg();
        arg.setEa(tenantAccount);
        GetUpstreamAndDownstreamEIList.Result result = enterpriseRelationAdapter.getUpstreamAndDownstreamTenantIdList(tenantId, arg);
        if (result != null && CollectionUtils.isNotEmpty(result.getDownstreamIds())) {
            result.getDownstreamIds().forEach(downEi -> {
                fieldDescribeAdapter.create(downEi, buildFieldArg(downEi, "CheckinsImgDetailObj", "Checkin.upper_ei"));
                fieldDescribeAdapter.create(downEi, buildFieldArg(downEi, "CheckinsImgObj", "Checkin.upper_ei"));
                fieldDescribeAdapter.create(downEi, buildFieldArg(downEi, "CheckinsObj", "Checkin.upper_ei"));
                fieldDescribeAdapter.create(downEi, buildFieldArg(downEi, "PurchaseDetailsObj", "Checkin.upper_ei"));
                fieldDescribeAdapter.create(downEi, buildFieldArg(downEi, "PurchaseReportingObj", "Checkin.upper_ei"));
                fieldDescribeAdapter.create(downEi, buildFieldArg(downEi, "ShelfReportObj", "Checkin.upper_ei"));
                fieldDescribeAdapter.create(downEi, buildFieldArg(downEi, "ShelfReportDetailObj", "Checkin.upper_ei"));
                fieldDescribeAdapter.create(downEi, buildFieldArg(downEi, "StockReportingDetailsObj", "Checkin.upper_ei"));
                fieldDescribeAdapter.create(downEi, buildFieldArg(downEi, "StockReportingObj", "Checkin.upper_ei"));
            });
        }

        // 添加渠道经理角色
        roleService.addChannelManagerRole(tenantId);

        // 添加经销商业务类型, 并修改默认业务类型名称
        recordTypeService.create(tenantId, initCreateRecordTypeArg());
        recordTypeService.update(tenantId, initUpdateRecordTypeArg());
    }

    @Override
    public void invalid(int tenantId, String tenantAccount, int userId) {
    }

    @Override
    public String upgrade(int tenantId) {
        logger.info("start upgrade FMCG.DDS. tenantId : {}", tenantId);

        for (String apiName : UPGRADE_API_NAMES) {
            try {
                Create.Arg arg = buildDescribeArg(apiName);
                if (arg != null) {
                    describeAdapter.create(tenantId, arg);
                }
            } catch (AdapterException ex) {
                logger.error("DDS package active error on using metadata adapter when create object.", ex);
            }
        }

        fieldDescribeAdapter.create(tenantId, buildFieldArg("DealerOrderObj", "DealerOrderObj.original_record_type"));
        return null;
    }

    @Override
    public String formatOwnerCode(int tenantId, String tenantAccount, int userId) {
        return "TENANT." + tenantId;
    }

    @Override
    public String getAppCode() {
        return "APP.DATA_SYNC";
    }

    private Add.Arg buildFieldArg(String describeApiName, String key) {
        Add.Arg arg = new Add.Arg();
        IChangeableConfig config = ConfigFactory.getConfig(DESCRIBE_CONFIG_ROOT);
        arg.setDescribeAPIName(describeApiName);
        arg.setGroupFields("[]");

        JSONObject field = JSON.parseObject(config.get(key));
        field.put("describe_api_name", describeApiName);

        arg.setFieldDescribe(field.toJSONString());

        JSONObject layOutDto = new JSONObject();
        layOutDto.put("is_show", true);
        layOutDto.put("is_required", field.getBoolean("is_required"));
        if ("quote".equals(field.getString("type"))) {
            layOutDto.put("is_readonly", true);
        } else {
            layOutDto.put("is_readonly", false);
        }

        layOutDto.put("render_type", field.getString("type"));
        layOutDto.put("api_name", field.getString("api_name"));
        layOutDto.put("is_default", true);
        layOutDto.put("label", field.getString("label"));

        JSONArray layOutList = new JSONArray(1);
        layOutList.add(layOutDto);
        arg.setLayoutList(layOutList.toJSONString());

        return arg;
    }

    private Create.Arg buildDescribeArg(String apiName) {
        IChangeableConfig config = ConfigFactory.getConfig(DESCRIBE_CONFIG_ROOT);
        String configJson = config.get(apiName);

        if (Strings.isNullOrEmpty(configJson)) {
            return null;
        }

        Create.Arg arg = new Create.Arg();

        arg.setActive(true);
        arg.setIncludeLayout(true);

        JSONObject describe = JSON.parseObject(configJson, Feature.OrderedField);
        arg.setJsonData(describe.toJSONString());

        JSONObject layOut = JSON.parseObject(config.get("common.layout"));
        layOut.put("ref_object_api_name", apiName);
        layOut.put("api_name", apiName + "_layout__c");
        JSONArray components = layOut.getJSONArray("components");
        JSONObject firstComponent = components.getJSONObject(0);
        JSONArray fieldSection = firstComponent.getJSONArray("field_section");
        JSONObject firstFieldSection = fieldSection.getJSONObject(0);
        JSONArray formFields = firstFieldSection.getJSONArray("form_fields");
        JSONObject fields = describe.getJSONObject("fields");

        List<JSONObject> customerFields = Lists.newArrayList();
        fields.keySet().forEach(key ->
        {
            if (!SYSTEM_FIELDS.contains(key)) {
                JSONObject field = fields.getJSONObject(key);
                JSONObject formField = new JSONObject();
                if ("quote".equals(field.getString("type"))) {
                    formField.put("is_readonly", true);
                } else {
                    formField.put("is_readonly", false);
                }
                formField.put("is_required", field.getBoolean("is_required"));
                formField.put("render_type", field.getString("type"));
                formField.put("field_name", field.getString("api_name"));
                customerFields.add(formField);
            }
        });

        formFields.addAll(0, customerFields);

        arg.setJsonLayout(layOut.toJSONString());
        JSONObject listLayOut = JSON.parseObject(config.get("common.list_layout"));
        listLayOut.put("ref_object_api_name", apiName);
        listLayOut.put("api_name", apiName + "_list_layout__c");
        arg.setJsonListLayout(listLayOut.toJSONString());

        arg.setLayoutType("detail");
        return arg;
    }

    private Add.Arg buildFieldArg(Integer tenantId, String describeApiName, String key) {
        Add.Arg arg = new Add.Arg();
        IChangeableConfig config = ConfigFactory.getConfig(DESCRIBE_CONFIG_ROOT);

        arg.setDescribeAPIName(describeApiName);
        arg.setGroupFields("[]");

        JSONObject field = JSON.parseObject(config.get(key));
        field.put("describe_api_name", describeApiName);

        arg.setFieldDescribe(field.toJSONString());

        JSONObject layOutDto = new JSONObject();
        layOutDto.put("is_show", true);
        layOutDto.put("is_required", field.getBoolean("is_required"));
        if (field.getString("type").equals("quote")) {
            layOutDto.put("is_readonly", true);
        } else {
            layOutDto.put("is_readonly", false);
        }
        layOutDto.put("render_type", field.getString("type"));
        layOutDto.put("api_name", queryDefaultLayout(tenantId, describeApiName));
        layOutDto.put("is_default", true);
        layOutDto.put("label", field.getString("label"));

        JSONArray layOutList = new JSONArray(1);
        layOutList.add(layOutDto);
        arg.setLayoutList(layOutList.toJSONString());
        return arg;
    }

    private RecordType.Arg initCreateRecordTypeArg() {
        RecordType.Arg arg = new RecordType.Arg();

        arg.setDescribeApiName(ACCOUNT_OBJ_API_NAME);
        RecordType.RecordTypeDto recordType = new RecordType.RecordTypeDto();
        recordType.setLabel(DEALER_RECORD_DISPLAY_NAME);
        recordType.setApiName(DEALER_RECORD_API_NAME);
        recordType.setDescription("");
        recordType.setActive(true);
        recordType.setRoles(Lists.newArrayList());
        RecordType.RecordTypeRoleDto roleDto = new RecordType.RecordTypeRoleDto();
        roleDto.setRoleCode("00000000000000000000000000000026");
        roleDto.setDefault(false);
        roleDto.setUsed(true);
        roleDto.setLayoutApiName(DEALER_DEFAULT_LAYOUT_API_NAME);
        recordType.getRoles().add(roleDto);
        arg.setRecordType(JSON.toJSONString(recordType));

        return arg;
    }

    private RecordType.Arg initUpdateRecordTypeArg() {

        RecordType.Arg arg = new RecordType.Arg();
        arg.setDescribeApiName("AccountObj");

        RecordType.RecordTypeDto recordType = new RecordType.RecordTypeDto();

        recordType.setLabel("终端");
        recordType.setApiName("default__c");
        recordType.setDescription("预设业务类型");
        recordType.setActive(true);
        recordType.setRoles(Lists.newArrayList());

        arg.setRecordType(JSON.toJSONString(recordType));

        return arg;
    }

    private String queryDefaultLayout(Integer tenantId, String apiName) {
        QueryLayout.Arg arg = new QueryLayout.Arg();
        arg.setObjectDescribeApiName(apiName);
        try {
            QueryLayout.Result r = dataAdapter.queryLayout(tenantId, arg);
            if (r.getLayouts() != null && !r.getLayouts().isEmpty()) {
                for (JSONObject v : r.getLayouts()) {
                    if ("默认布局".equals(v.getString("display_name"))) {
                        return v.getString("api_name");
                    }
                }
            }
        } catch (Exception e) {
            return apiName;
        }
        return apiName;
    }
}
