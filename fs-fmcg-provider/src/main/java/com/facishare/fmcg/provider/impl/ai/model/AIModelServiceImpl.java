package com.facishare.fmcg.provider.impl.ai.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.ai.detector.api.enumeration.ModelManufacturerEnum;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.ai.AIServiceAdapter;
import com.facishare.fmcg.adapter.license.LicenseAdapter;
import com.facishare.fmcg.adapter.license.dto.ValidateLicense;
import com.facishare.fmcg.adapter.util.ConvertUtil;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.model.AIDetectRuleDTO;
import com.facishare.fmcg.api.dto.ai.model.AddModel;
import com.facishare.fmcg.api.dto.ai.model.AddToken;
import com.facishare.fmcg.api.dto.ai.model.BatchDeleteObjectMap;
import com.facishare.fmcg.api.dto.ai.model.BatchQueryAIRuleByIds;
import com.facishare.fmcg.api.dto.ai.model.BatchQueryModel;
import com.facishare.fmcg.api.dto.ai.model.BatchSaveOrUpdateObjectMap;
import com.facishare.fmcg.api.dto.ai.model.DeleteDetectRule;
import com.facishare.fmcg.api.dto.ai.model.DisplaySceneDTO;
import com.facishare.fmcg.api.dto.ai.model.GetAIModelScenes;
import com.facishare.fmcg.api.dto.ai.model.GetAIModelsByScene;
import com.facishare.fmcg.api.dto.ai.model.GetAIRuleById;
import com.facishare.fmcg.api.dto.ai.model.GetAIRuleDescribe;
import com.facishare.fmcg.api.dto.ai.model.GetDisplayScenesByModelId;
import com.facishare.fmcg.api.dto.ai.model.GetModelById;
import com.facishare.fmcg.api.dto.ai.model.GetModelDescribe;
import com.facishare.fmcg.api.dto.ai.model.GetModelDescribe.ModelField;
import com.facishare.fmcg.api.dto.ai.model.GetModelDescribe.ModelManufacturer;
import com.facishare.fmcg.api.dto.ai.model.GetModelDescribe.ModelType;
import com.facishare.fmcg.api.dto.ai.model.GetModelDescribe.Result;
import com.facishare.fmcg.api.dto.ai.model.ModelDTO;
import com.facishare.fmcg.api.dto.ai.model.ModelSwitch;
import com.facishare.fmcg.api.dto.ai.model.ObjectMapDTO;
import com.facishare.fmcg.api.dto.ai.model.QueryObjectList;
import com.facishare.fmcg.api.dto.ai.model.QueryRuleList;
import com.facishare.fmcg.api.dto.ai.model.SaveOrUpdateDetectRule;
import com.facishare.fmcg.api.dto.ai.model.TokenInfoDTO;
import com.facishare.fmcg.api.dto.ai.model.UpdateModel;
import com.facishare.fmcg.api.error.ErrorCode;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.ai.model.AIModelService;
import com.facishare.fmcg.api.util.I18N;
import com.facishare.fmcg.provider.abstraction.ServiceBase;
import com.facishare.fmcg.provider.constant.ModelSceneEnum;
import com.facishare.fmcg.provider.dao.abstraction.PromptTemplateDAO;
import com.facishare.fmcg.provider.dao.po.PromptTemplatePO;
import com.facishare.fmcg.provider.impl.notice.FastNoticeService;
import com.fmcg.framework.http.CheckinProxy;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.TPMProxy;
import com.fmcg.framework.http.contract.checkin.GetActionByModelId;
import com.fmcg.framework.http.contract.checkin.GetActionByRuleId;
import com.fmcg.framework.http.contract.paas.data.PaasDataQueryWithFields;
import com.fmcg.framework.http.contract.tpm.QueryByModelAndRule;
import com.fxiaoke.api.IdGenerator;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Component
public class AIModelServiceImpl extends ServiceBase implements AIModelService {

    public static Map<String, List<String>> MANUFACTURER_MAP = new HashMap<>();

    @Resource
    private AIServiceAdapter aiServiceAdapter;

    @Resource
    private FastNoticeService fastNoticeService;

    @Resource
    private CheckinProxy checkinProxy;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private TPMProxy tpmProxy;

    @Resource
    private PromptTemplateDAO promptTemplateDAO;

    @Resource
    private LicenseAdapter licenseAdapter;

    private static Map<String, List<String>> AI_RULE_CAPABILITY_MAP = new HashMap<>();

    private static Map<String, Map<String, Integer>> OBJECT_MAP_CAPABILITY_MAP = new HashMap<>();

    static {
        ConfigFactory.getConfig("fs-fmcg-sdk-apis", config -> {
            String manufacturer = config.get("ai.model.manufacturer");
            if (!StringUtils.isEmpty(manufacturer)) {
                MANUFACTURER_MAP = JSON.parseObject(manufacturer, new TypeReference<Map<String, List<String>>>() {
                });
            }
            if (!MANUFACTURER_MAP.containsKey("default")) {
                MANUFACTURER_MAP.put("default", Lists.newArrayList("baidu", "tu_jiang", "sense_time", "huawei", "fs"));
            }

            String aiRuleCapability = config.get("ai.rule.capability");
            if (!Strings.isNullOrEmpty(aiRuleCapability)) {
                AI_RULE_CAPABILITY_MAP = JSON.parseObject(aiRuleCapability,
                        new TypeReference<Map<String, List<String>>>() {
                        });
            }
            if (!AI_RULE_CAPABILITY_MAP.containsKey("default")) {
                AI_RULE_CAPABILITY_MAP.put("default", Lists.newArrayList("isOpenProductRowNumber"));
            }

            String objectMapCapability = config.get("object.map.capability");
            if (!Strings.isNullOrEmpty(objectMapCapability)) {
                OBJECT_MAP_CAPABILITY_MAP = JSON.parseObject(objectMapCapability,
                        new TypeReference<Map<String, Map<String, Integer>>>() {
                        });
                if (!OBJECT_MAP_CAPABILITY_MAP.containsKey("default")) {
                    Map<String, Integer> defaultOptions = new HashMap<>();
                    defaultOptions.put("isShowUnionType", 1);
                    OBJECT_MAP_CAPABILITY_MAP.put("default", defaultOptions);
                }
            }
        });
    }

    @Override
    public ApiResult<GetAIModelScenes.Result> getAIModelScenes(ApiArg<GetAIModelScenes.Arg> arg) {
        GetAIModelScenes.Result result = new GetAIModelScenes.Result();
        result.setModelScenes(Arrays.stream(ModelSceneEnum.values()).filter(scene -> {
            if (CollectionUtils.isEmpty(scene.supportLicenses())) {
                return true;
            }
            return licenseAdapter.validate(arg.getTenantId(), scene.supportLicenses());
        }).map(v -> GetAIModelScenes.ModelScene.builder().name(v.getI18nName()).code(v.code()).build()).collect(Collectors.toList()));
        return setSuccess(result);
    }


    @Override
    public ApiResult<GetAIModelsByScene.Result> getAIModelsByScene(ApiArg<GetAIModelsByScene.Arg> arg) {
        GetAIModelsByScene.Result result = new GetAIModelsByScene.Result();
        try {
            List<ModelDTO> models = aiServiceAdapter.getModelList(arg.getTenantId(), arg.getData().getScene());
            models.stream().forEach(model -> fillAdditionCapability(arg.getTenantId(), model));
            result.setModels(models);
            return setSuccess(result);
        } catch (FmcgException fmcgException) {
            log.error("getAIModelsByScene error", fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("getAIModelsByScene error", e);
            return setFailure(ErrorCode.AI_MODEL_LIST_ERROR);
        }
    }

    @Override
    public ApiResult<GetModelById.Result> getModelById(ApiArg<GetModelById.Arg> arg) {
        try {
            GetModelById.Result result = aiServiceAdapter.getModelById(
                    arg.getTenantId(),
                    arg.getData().getModelId(),
                    arg.getData().isNeedObjectMap(),
                    arg.getData().isNeedRule(),
                    false);
            if (Objects.nonNull(result.getModel())) {
                fillAdditionCapability(arg.getTenantId(), result.getModel());
            }
            fillRealObjectName(arg.getTenantId(), result.getObjectMaps());

            return setSuccess(result);
        } catch (FmcgException fmcgException) {
            log.error("getModelById error", fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("getModelById error, modelId: {}", arg.getData().getModelId(), e);
            return setFailure(ErrorCode.MODEL_DETAIL_ERROR);
        }
    }

    private void fillAdditionCapability(Integer tenantId, ModelDTO modelDTO) {
        if (CollectionUtils.isEmpty(modelDTO.getAiRuleCapabilities())) {
            String capabilityKey = tenantId + "." + modelDTO.getModelManufacturer();
            String capabilityTypeKey = modelDTO.getType() + "." + modelDTO.getModelManufacturer();
            if (AI_RULE_CAPABILITY_MAP.containsKey(capabilityKey)) {
                modelDTO.setAiRuleCapabilities(AI_RULE_CAPABILITY_MAP.get(capabilityKey));
            } else if (AI_RULE_CAPABILITY_MAP.containsKey(capabilityTypeKey)) {
                modelDTO.setAiRuleCapabilities(AI_RULE_CAPABILITY_MAP.get(capabilityTypeKey));
            } else {
                capabilityKey = modelDTO.getModelManufacturer();
                modelDTO.setAiRuleCapabilities(AI_RULE_CAPABILITY_MAP.getOrDefault(capabilityKey, AI_RULE_CAPABILITY_MAP.get("default")));
            }
        }
        if (CollectionUtils.isEmpty(modelDTO.getObjectMapCapabilityMap())) {
            modelDTO.setObjectMapCapabilityMap(OBJECT_MAP_CAPABILITY_MAP.getOrDefault(String.valueOf(tenantId),
                    OBJECT_MAP_CAPABILITY_MAP.get("default")));
        }
    }

    @Override
    public ApiResult<ModelSwitch.Result> modelSwitch(ApiArg<ModelSwitch.Arg> arg) {
        try {
            //todo:门头模型没有映射规则
            if (arg.getData().getOperation() == 1) {
                ModelDTO modelDTO = aiServiceAdapter.getModelById(arg.getTenantId(), arg.getData().getModelId(), false, false, false).getModel();
                List<AIDetectRuleDTO> aiDetectRuleDTOS = aiServiceAdapter
                        .queryAIDetectRuleListByModelId(arg.getTenantId(), arg.getData().getModelId());
                List<ObjectMapDTO> objectMapDTOList = aiServiceAdapter.getObjectMap(arg.getTenantId(),
                        arg.getData().getModelId());
                if (modelDTO.getType().equals("storeFrontDetect")) {
                    if (CollectionUtils.isEmpty(aiDetectRuleDTOS)) {
                        return setFailure(ErrorCode.NEED_OBJECT_MAP_AND_AI_DETECT_RULE);
                    }
                } else if (modelDTO.getType().equals("OBJECT_RECOGNITION")) {
                    if (CollectionUtils.isEmpty(aiDetectRuleDTOS) || CollectionUtils.isEmpty(objectMapDTOList)) {
                        return setFailure(ErrorCode.NEED_OBJECT_MAP_AND_AI_DETECT_RULE);
                    }
                }

            } else {
                if (modelHasBeenRelated(arg.getTenantId(), arg.getData().getModelId())) {
                    return setFailure(ErrorCode.MODEL_HAS_BEEN_RELATED);
                }
            }
            aiServiceAdapter.modelSwitch(
                    arg.getTenantId(),
                    arg.getData().getModelId(),
                    arg.getData().getOperation());
            return setSuccess(new ModelSwitch.Result());
        } catch (FmcgException fmcgException) {
            log.error("modelSwitch error", fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("modelSwitch error", e);
            return setFailure(ErrorCode.MODEL_STATUS_SWITCH_ERROR);
        }
    }

    @Override
    public ApiResult<AddModel.Result> addModel(ApiArg<AddModel.Arg> arg) {
        try {
            ModelDTO modelDTO = arg.getData().getModel();
            if (Strings.isNullOrEmpty(modelDTO.getScene())) {
                modelDTO.setScene("display");
            }
            if (Strings.isNullOrEmpty(modelDTO.getParentType())) {
                modelDTO.setParentType("Tradition");
            }
            modelDTO.setTenantId(arg.getTenantId());
            fillPlatform(modelDTO);
            fillToken(modelDTO);

            ModelDTO resultModel = aiServiceAdapter.addModel(arg.getTenantId(), arg.getUserId(), modelDTO);

            AddModel.Result result = new AddModel.Result();
            result.setModel(resultModel);
            return setSuccess(result);
        } catch (FmcgException fmcgException) {
            log.error("addModel error", fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("addModel error", e);
            return setFailure(ErrorCode.MODEL_ADD_ERROR);
        }
    }

    private void fillPlatform(ModelDTO modelDTO) {
        if (Strings.isNullOrEmpty(modelDTO.getPlatform())) {
            ModelManufacturerEnum manufacturerEnum = ModelManufacturerEnum.getByValue(modelDTO.getModelManufacturer());
            switch (manufacturerEnum) {
                case SENSE_TIME:
                    modelDTO.setPlatform("sense_time");
                    break;
                case HUAWEI:
                    modelDTO.setPlatform("huawei_model_art_obj_detect");
                    break;
                case TU_JIANG:
                    modelDTO.setPlatform("tujiang");
                    break;
                case BAIDU:
                    modelDTO.setPlatform("baidu");
                    break;
                case RIO:
                    modelDTO.setPlatform("rio");
                    break;
                case MENG_NIU:
                    modelDTO.setPlatform("mengniu_v2");
                    break;
                case FS:
                    modelDTO.setPlatform("openai_vlm");
                    break;
                default:
                    break;
            }
        }
    }

    private void fillToken(ModelDTO modelDTO) {
        if (modelDTO == null) {
            return;
        }
        TokenInfoDTO tokenInfoDTO = ConvertUtil.convertToTokenInfoDTO(modelDTO);
        tokenInfoDTO = aiServiceAdapter.addTokenInfo(modelDTO.getTenantId(), tokenInfoDTO);
        if (tokenInfoDTO != null) {
            modelDTO.setTokenInfo(tokenInfoDTO);
            modelDTO.setToken_identityKey(tokenInfoDTO.getIdentityKey());
        }
    }

    @Override
    public ApiResult<UpdateModel.Result> updateModel(ApiArg<UpdateModel.Arg> arg) {
        try {
            ModelDTO modelDTO = arg.getData().getModel();
            if (arg.getData().isUpdateToken()) {
                arg.getData().getModel().setToken_identityKey(null);
                fillToken(modelDTO);
            } else {
                modelDTO.setToken_identityKey(null);
            }
            ModelDTO resultModel = aiServiceAdapter.overloadUpdateModel(arg.getTenantId(), arg.getUserId(), modelDTO);

            UpdateModel.Result result = new UpdateModel.Result();
            freshModel(arg.getTenantId(), arg.getUserId(), resultModel.getId());
            if (arg.getData().isUpdateToken()) {
                refreshToken(String.valueOf(arg.getUserId()), arg.getData().getModel().getToken_identityKey());
            }
            return setSuccess(result);
        } catch (FmcgException e) {
            log.info("updateModel error", e);
            return setFailure(e);
        } catch (Exception e) {
            log.info("updateModel error", e);
            return setFailure(ErrorCode.MODEL_UPDATE_ERROR);
        }
    }

    private void refreshToken(String userId, String identityKey) {
        fastNoticeService.SendNotice("FMCG_AI_SDK_TOKEN_CACHE_REFRESH", identityKey, userId);
    }

    @Override
    public ApiResult<GetModelDescribe.Result> getModelDescribe(ApiArg<GetModelDescribe.Arg> arg) {
        GetModelDescribe.Result result = new GetModelDescribe.Result();
        String scene = Strings.isNullOrEmpty(arg.getData().getScene()) ? "display" : arg.getData().getScene();
        String jsonPath = getJsonFilePathByScene(arg.getTenantId(), scene);
        try {
            // 从resources目录读取modelDescribe.json文件
            ClassPathResource resource = new ClassPathResource(jsonPath);
            String modelDescribeJson = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
            JSONObject modelDescribe = JSON.parseObject(modelDescribeJson);

            // 解析厂商信息
            result.setModelManufacturer(JSON.parseArray(
                    modelDescribe.getString("modelManufacturer"),
                    GetModelDescribe.ModelManufacturer.class));

            // 解析类型信息
            result.setType(JSON.parseArray(
                    modelDescribe.getString("type"),
                    GetModelDescribe.ModelType.class));

            // 解析模型信息映射
            JSONObject modelInfoMapJson = modelDescribe.getJSONObject("modelInfoMap");
            Map<String, List<GetModelDescribe.ModelInfo>> modelInfoMap = new HashMap<>();

            for (String key : modelInfoMapJson.keySet()) {
                List<GetModelDescribe.ModelInfo> modelInfoList = JSON.parseArray(
                        modelInfoMapJson.getString(key),
                        GetModelDescribe.ModelInfo.class);
                modelInfoMap.put(key, modelInfoList);
            }
            result.setModelInfoMap(modelInfoMap);

            filterManufacturer(arg.getTenantId(), result);
            // 填充国际化信息
            fillI18nInfo(result);

            return setSuccess(result);
        } catch (Exception e) {
            log.error("getModelDescribe error", e);
            return setFailure(ErrorCode.MODEL_DESCRIPTION_ERROR);
        }
    }

    private String getJsonFilePathByScene(Integer tenantId, String scene) {
        ModelSceneEnum sceneEnum = ModelSceneEnum.getModelSceneEnum(scene);
        switch (sceneEnum) {
            case DOOR_PHOTO_RECOGNITION:
                return "model/storeFrontModelDescribe.json";
            case DISPLAY:
            default:
                return "model/displayModelDescribe.json";
        }
    }

    private void fillI18nInfo(Result result) {
        // 处理厂商名称国际化
        result.getModelManufacturer().forEach(this::fillManufacturerI18n);

        // 处理类型名称国际化
        result.getType().forEach(this::fillTypeI18n);

        // 处理字段标签和提示信息国际化
        result.getModelInfoMap().values()
                .forEach(modelInfos -> modelInfos.forEach(
                        modelInfo -> modelInfo.getField().forEach(this::fillFieldI18n)));
    }

    private void fillManufacturerI18n(ModelManufacturer manufacturer) {
        manufacturer.setName(I18N.getOrDefaultByOriginalKey(manufacturer.getI18nKey(), manufacturer.getName()));
    }

    private void fillTypeI18n(ModelType type) {
        type.setName(I18N.getOrDefaultByOriginalKey(type.getI18nKey(), type.getName()));
        type.setTip(I18N.getOrDefaultByOriginalKey(type.getTipI18nKey(), type.getTip()));
    }

    private void fillFieldI18n(ModelField field) {
        field.setLabel(I18N.getOrDefaultByOriginalKey(field.getI18nKey(), field.getLabel()));
        field.setTip(I18N.getOrDefaultByOriginalKey(field.getTipI18nKey(), field.getTip()));
    }

    private void filterManufacturer(Integer tenantId, Result result) {
        List<String> manufacturers = MANUFACTURER_MAP.getOrDefault(String.valueOf(tenantId),
                MANUFACTURER_MAP.get("default"));
        if (!CollectionUtils.isEmpty(manufacturers)) {
            result.getModelManufacturer().removeIf(manufacturer -> !manufacturers.contains(manufacturer.getValue()));
        }
    }

    @Override
    public ApiResult<QueryObjectList.Result> queryObjectList(ApiArg<QueryObjectList.Arg> arg) {
        QueryObjectList.Result result = new QueryObjectList.Result();
        try {
            // 根据modelId查询对象映射列表
            String modelId = arg.getData().getModelId();
            if (Strings.isNullOrEmpty(modelId)) {
                result.setObjectMaps(new ArrayList<>());
                return setSuccess(result);
            }
            List<ObjectMapDTO> objectMaps = aiServiceAdapter.getObjectMap(arg.getTenantId(), modelId);
            fillRealObjectName(arg.getTenantId(), objectMaps);
            result.setObjectMaps(objectMaps);
            return setSuccess(result);
        } catch (FmcgException fmcgException) {
            log.error("queryObjectList error, modelId: {}", arg.getData().getModelId(), fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("queryObjectList error, modelId: {}", arg.getData().getModelId(), e);
            return setFailure(ErrorCode.OBJECT_MAP_QUERY_ERROR);
        }
    }

    @Override
    public ApiResult<BatchDeleteObjectMap.Result> batchDeleteObjectMap(ApiArg<BatchDeleteObjectMap.Arg> arg) {
        BatchDeleteObjectMap.Result result = new BatchDeleteObjectMap.Result();
        try {
            // 批量删除对象映射
            List<String> ids = arg.getData().getIds();
            if (CollectionUtils.isEmpty(ids)) {
                return setFailure(ErrorCode.PARAM_DELETE_IDS_EMPTY);
            }

            aiServiceAdapter.batchDeleteObjectMap(arg.getTenantId(), ids);
            return setSuccess(result);
        } catch (FmcgException fmcgException) {
            log.error("batchDeleteObjectMap error, ids: {}", arg.getData().getIds(), fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("batchDeleteObjectMap error, ids: {}", arg.getData().getIds(), e);
            return setFailure(ErrorCode.OBJECT_MAP_DELETE_ERROR);
        }
    }

    @Override
    public ApiResult<BatchSaveOrUpdateObjectMap.Result> batchSaveOrUpdateObjectMap(
            ApiArg<BatchSaveOrUpdateObjectMap.Arg> arg) {
        BatchSaveOrUpdateObjectMap.Result result = new BatchSaveOrUpdateObjectMap.Result();
        try {
            String modelId = "";
            List<ObjectMapDTO> objectMaps = arg.getData().getObjectMaps();
            if (CollectionUtils.isEmpty(objectMaps)) {
                return setFailure(ErrorCode.PARAM_OBJECT_MAP_EMPTY);
            }
            if (objectMaps.stream().anyMatch(objectMap -> StringUtils.isEmpty(objectMap.getObjectId()))) {
                return setFailure(ErrorCode.OBJECT_MAP_DO_NOT_HAS_OBJECT_ID);
            }

            // 区分新增和更新
            List<ObjectMapDTO> toInsert = new ArrayList<>();
            List<ObjectMapDTO> toUpdate = new ArrayList<>();

            for (ObjectMapDTO objectMap : objectMaps) {
                if (StringUtils.isEmpty(objectMap.getId())) {
                    objectMap.setId(IdGenerator.get());
                    toInsert.add(objectMap);
                } else {
                    toUpdate.add(objectMap);
                }
            }

            // 批量插入
            if (!toInsert.isEmpty()) {
                modelId = toInsert.get(0).getModelId();
                aiServiceAdapter.batchAddObjectMap(arg.getTenantId(), toInsert);
            }

            // 批量更新
            if (!toUpdate.isEmpty()) {
                modelId = toUpdate.get(0).getModelId();
                aiServiceAdapter.batchUpdateObjectMapDTO(arg.getTenantId(), toUpdate);
            }

            result.setObjectMaps(objectMaps);
            freshModel(arg.getTenantId(), arg.getUserId(), modelId);
            return setSuccess(result);
        } catch (FmcgException fmcgException) {
            log.error("batchSaveOrUpdateObjectMap error", fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("batchSaveOrUpdateObjectMap error", e);
            return setFailure(ErrorCode.OBJECT_MAP_SAVE_UPDATE_ERROR);
        }
    }

    private void freshModel(Integer tenantId, Integer userId, String modelId) {
        fastNoticeService.SendNotice("FMCG_AI_SDK_CACHE_REFRESH",
                String.format("%s.%s", tenantId, modelId), String.valueOf(userId));
    }

    @Override
    public ApiResult<QueryRuleList.Result> queryRuleList(ApiArg<QueryRuleList.Arg> arg) {
        QueryRuleList.Result result = new QueryRuleList.Result();
        try {
            // 根据modelId查询规则列表
            String modelId = arg.getData().getModelId();
            List<AIDetectRuleDTO> rules = aiServiceAdapter.queryAIDetectRuleListByModelId(arg.getTenantId(), modelId);
            fillAssociatedBizNames(arg.getTenantId(), modelId, rules);
            result.setRules(rules);
            return setSuccess(result);
        } catch (FmcgException fmcgException) {
            log.error("queryRuleList error, modelId: {}", arg.getData().getModelId(), fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("queryRuleList error, modelId: {}", arg.getData().getModelId(), e);
            return setFailure(ErrorCode.RULE_LIST_QUERY_ERROR);
        }
    }

    private void fillAssociatedBizNames(Integer tenantId, String modelId, List<AIDetectRuleDTO> rules) {
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        List<GetActionByModelId.Action> actions = checkinProxy.getActionByModelId(tenantId, ea, modelId);
        Map<String, AIDetectRuleDTO> ruleDTOMap = rules.stream().collect(Collectors.toMap(AIDetectRuleDTO::getId, v -> v, (a, b) -> a));
        actions.forEach(action -> {
            AIDetectRuleDTO rule = ruleDTOMap.get(action.getAiRuleId());
            if (Objects.nonNull(rule)) {
                rule.getAssociatedBizNames().add("外勤动作：" + action.getName());
            }
        });
        List<QueryByModelAndRule.ActivityTypeItem> activityTypeItems = getActivityTypeItem(tenantId, modelId, null);
        activityTypeItems.forEach(activityTypeItem -> {
            AIDetectRuleDTO rule = ruleDTOMap.get(activityTypeItem.getRuleId());
            if (Objects.nonNull(rule)) {
                rule.getAssociatedBizNames().add("活动类型：" + activityTypeItem.getName());
            }
        });
    }

    private List<QueryByModelAndRule.ActivityTypeItem> getActivityTypeItem(Integer tenantId, String modelId, String ruleId) {
        try {
            QueryByModelAndRule.Arg arg = new QueryByModelAndRule.Arg();
            arg.setModelId(modelId);
            arg.setRuleId(ruleId);
            QueryByModelAndRule.Result result = tpmProxy.queryByModelAndRule(tenantId, -10000, arg);
            if (result.getCode() != 0) {
                log.info("getActivityTypeItem error, modelId: {},result: {}", modelId, result);
                throw new FmcgException(result.getMessage(), result.getCode());
            } else {
                return result.getData().getActivityTypeList();
            }
        } catch (Exception e) {
            log.info("query activity error: {}", e);
            return new ArrayList<>();
        }
    }

    private void fillRealObjectName(Integer tenantId, List<ObjectMapDTO> objectMapDTOs) {
        if (CollectionUtils.isEmpty(objectMapDTOs)) {
            return;
        }
        // 需要将rule里的dataId按apiName
        Map<String, Set<String>> dataIdMap = new HashMap<>();
        for (ObjectMapDTO objectMapDTO : objectMapDTOs) {
            if (!dataIdMap.containsKey(objectMapDTO.getApiName())) {
                dataIdMap.put(objectMapDTO.getApiName(), Sets.newHashSet(objectMapDTO.getObjectId()));
            } else {
                dataIdMap.get(objectMapDTO.getApiName()).add(objectMapDTO.getObjectId());
            }
        }
        Map<String, String> idNameMap = new HashMap<>();
        // 根据dataIdMap查询真实对象名称
        for (String apiName : dataIdMap.keySet()) {
            Set<String> dataIds = dataIdMap.get(apiName);
            List<JSONObject> dataList = queryObjectByIds(tenantId, apiName, new ArrayList<>(dataIds));
            dataList.forEach(data -> idNameMap.put(data.getString("_id"), data.getString("name")));
        }
        objectMapDTOs.forEach(objectMapDTO -> {
            if (idNameMap.containsKey(objectMapDTO.getObjectId())) {
                objectMapDTO.setObjectName(idNameMap.get(objectMapDTO.getObjectId()));
            }
        });
    }

    private List<JSONObject> queryObjectByIds(Integer tenantId, String apiName, List<String> dataIds) {
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        arg.setFieldList(Lists.newArrayList("_id", "name"));
        PaasDataQueryWithFields.QueryDTO queryDTO = new PaasDataQueryWithFields.QueryDTO();
        queryDTO.setLimit(1000);
        queryDTO.setOffset(0);
        queryDTO.setFilters(Lists.newArrayList(new PaasDataQueryWithFields.FilterDTO("_id", "IN", dataIds)));
        arg.setQueryString(JSON.toJSONString(queryDTO));
        PaasDataQueryWithFields.Result result = paasDataProxy.queryWithFields(tenantId, -10000, apiName, arg);
        return result.getResult().getQueryResult().getDataList();
    }

    @Override
    public ApiResult<SaveOrUpdateDetectRule.Result> saveOrUpdateDetectRule(ApiArg<SaveOrUpdateDetectRule.Arg> arg) {
        if (arg == null || arg.getTenantId() == null || arg.getData().getAiDetectRuleDTO() == null) {
            return setFailure(ErrorCode.PARAM_EMPTY);
        }

        try {
            AIDetectRuleDTO ruleDTO = arg.getData().getAiDetectRuleDTO();
            SaveOrUpdateDetectRule.Result result = new SaveOrUpdateDetectRule.Result();

            // 检查规则ID是否存在
            if (StringUtils.isEmpty(ruleDTO.getId())) {
                // 新增规则
                AIDetectRuleDTO savedRule = aiServiceAdapter.addAIDetectRule(arg.getTenantId(), ruleDTO);
                if (savedRule == null) {
                    return setFailure(ErrorCode.AI_DETECT_RULE_ADD_ERROR);
                }

                result.setRule(savedRule);
                return setSuccess(result);
            } else {
                // 更新规则
                AIDetectRuleDTO updatedRule = aiServiceAdapter.updateAIDetectRule(
                        arg.getTenantId(),
                        arg.getUserId(),
                        ruleDTO);

                if (updatedRule == null) {
                    return setFailure(ErrorCode.AI_DETECT_RULE_UPDATE_ERROR);
                }

                result.setRule(updatedRule);
                return setSuccess(result);
            }
        } catch (FmcgException e) {
            log.error("保存或更新AI检测规则失败, arg={}", arg, e);
            return setFailure(e);
        } catch (Exception e) {
            log.error("保存或更新AI检测规则发生未知异常, arg={}", arg, e);
            return setFailure(ErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public ApiResult<DeleteDetectRule.Result> deleteDetectRule(ApiArg<DeleteDetectRule.Arg> arg) {
        try {
            validateRuleRelated(arg.getTenantId(), arg.getData().getRuleId());
            AIDetectRuleDTO ruleDTO = aiServiceAdapter.getAIDetectRuleById(arg.getTenantId(), arg.getData().getRuleId());
            if (ruleDTO.isDefault()) {
                return setFailure(ErrorCode.AI_DETECT_DEFAULT_RULE_DELETE_ERROR);
            }
            aiServiceAdapter.deleteAIDetectRule(arg.getTenantId(), arg.getUserId(), arg.getData().getRuleId());
            return setSuccess(new DeleteDetectRule.Result());
        } catch (FmcgException fmcgException) {
            log.error("删除AI检测规则失败, ruleId={}", arg.getData().getRuleId(), fmcgException);
            return setFailure(fmcgException.getErrCode(), fmcgException.getErrMsg());
        } catch (Exception e) {
            log.error("删除AI检测规则发生未知异常, ruleId={}", arg.getData().getRuleId(), e);
            return setFailure(ErrorCode.AI_DETECT_RULE_DELETE_ERROR);
        }
    }

    @Override
    public ApiResult<BatchQueryModel.Result> batchQueryModel(ApiArg<BatchQueryModel.Arg> arg) {
        try {
            BatchQueryModel.Result result = new BatchQueryModel.Result();
            // 实现批量查询模型的逻辑
            return setSuccess(result);
        } catch (FmcgException fmcgException) {
            log.error("批量查询模型失败", fmcgException);
            return setFailure(fmcgException.getErrCode(), fmcgException.getErrMsg());
        } catch (Exception e) {
            log.error("批量查询模型发生未知异常", e);
            return setFailure(ErrorCode.MODEL_BATCH_QUERY_ERROR);
        }
    }

    @Override
    public ApiResult<BatchQueryAIRuleByIds.Result> batchQueryAIRuleByIds(ApiArg<BatchQueryAIRuleByIds.Arg> arg) {
        try {
            BatchQueryAIRuleByIds.Result result = new BatchQueryAIRuleByIds.Result();
            // 解析queryIds 通过&&拆分 并且前半部分为modelId 后半部分为ruleId
            List<String> ruleIds = new ArrayList<>();
            for (String queryId : arg.getData().getQueryIds()) {
                String[] parts = queryId.split("&&");
                String ruleId = parts[1];
                ruleIds.add(ruleId);
            }
            List<AIDetectRuleDTO> rules = aiServiceAdapter.queryAIDetectRulesByIds(arg.getTenantId(), ruleIds);
            result.setRules(rules);
            return setSuccess(result);
        } catch (FmcgException fmcgException) {
            log.error("batchQueryAIRuleByIds error", fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("batchQueryAIRuleByIds error", e);
            return setFailure(ErrorCode.AI_RULE_BATCH_QUERY_ERROR);
        }
    }

    @Override
    public ApiResult<GetAIRuleById.Result> getAIRuleById(ApiArg<GetAIRuleById.Arg> arg) {
        try {
            GetAIRuleById.Result result = new GetAIRuleById.Result();
            AIDetectRuleDTO rule = aiServiceAdapter.getAIDetectRuleById(arg.getTenantId(), arg.getData().getRuleId());
            if (rule == null) {
                throw new FmcgException(ErrorCode.AI_DETECT_RULE_NOT_FOUND);
            }
            fillAssociatedBizNames(arg.getTenantId(), rule.getModelId(), Lists.newArrayList(rule));
            result.setRule(rule);
            return setSuccess(result);
        } catch (FmcgException fmcgException) {
            log.error("getAIRuleById error", fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("getAIRuleById error, ruleId: {}", arg.getData().getRuleId(), e);
            return setFailure(ErrorCode.AI_DETECT_RULE_QUERY_BUSINESS_ERROR);
        }
    }

    private Boolean modelHasBeenRelated(Integer tenantId, String modelId) {
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        List<GetActionByModelId.Action> actions = checkinProxy.getActionByModelId(tenantId, ea, modelId);

        if (!CollectionUtils.isEmpty(actions)) {
            return true;
        }
        List<QueryByModelAndRule.ActivityTypeItem> items = getActivityTypeItem(tenantId, modelId, null);
        return !CollectionUtils.isEmpty(items);
    }

    private void validateRuleRelated(Integer tenantId, String ruleId) {

        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        List<GetActionByRuleId.Action> actions = checkinProxy.getActionByRuleId(tenantId, ea, ruleId);
        if (!CollectionUtils.isEmpty(actions)) {
            throw new FmcgException(ErrorCode.RULE_HAS_BEEN_RELATED);
        }
        List<QueryByModelAndRule.ActivityTypeItem> items = getActivityTypeItem(tenantId, null, ruleId);
        if (!CollectionUtils.isEmpty(items)) {
            throw new FmcgException(ErrorCode.RULE_HAS_BEEN_RELATED);
        }
    }

    @Override
    public ApiResult<GetAIRuleDescribe.Result> getAIRuleDescribe(ApiArg<GetAIRuleDescribe.Arg> arg) {
        GetAIRuleDescribe.Result result = new GetAIRuleDescribe.Result();
        try {
            arg.getData().validate();
            GetModelById.Result modelResult = aiServiceAdapter.getModelById(arg.getTenantId(), arg.getData().getModelId(), false, false, false);

            // 从resources目录读取aiRuleDescribe.json文件
            ClassPathResource resource = new ClassPathResource("model/aiRuleDescribe.json");
            String aiRuleDescribeJson = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
            JSONObject aiRuleDescribe = JSON.parseObject(aiRuleDescribeJson);

            // 解析分组列表
            result.setGroupList(JSON.parseArray(
                    aiRuleDescribe.getString("groupList"),
                    GetAIRuleDescribe.GroupItem.class));

            // 解析检测能力列表
            result.setDetectCapabilityList(JSON.parseArray(
                    aiRuleDescribe.getString("detectCapabilityList"),
                    GetAIRuleDescribe.DetectCapabilityItem.class));

            // 解析字段映射
            JSONObject fieldsMapJson = aiRuleDescribe.getJSONObject("fieldsMap");
            Map<String, GetAIRuleDescribe.FieldMapItem> fieldsMap = new HashMap<>();


            for (String key : fieldsMapJson.keySet()) {
                GetAIRuleDescribe.FieldMapItem fieldMapItem = JSON.parseObject(
                        fieldsMapJson.getString(key),
                        GetAIRuleDescribe.FieldMapItem.class);
                fieldsMap.put(key, fieldMapItem);
                if ("LLM".equals(modelResult.getModel().getParentType()) && "promptTemplate".equals(key)) {
                    loadPromptTemplate(arg.getTenantId(), fieldMapItem, modelResult.getModel().getType());
                }
            }
            result.setFieldsMap(fieldsMap);

            // 解析分组字段关系列表
            result.setGroupFieldRelationList(JSON.parseArray(
                    aiRuleDescribe.getString("groupFieldRelationList"),
                    GetAIRuleDescribe.GroupFieldRelation.class));

            // 解析检测能力字段关系映射
            JSONObject detectCapabilityFieldRelationMapJson = aiRuleDescribe.getJSONObject("detectCapabilityFieldRelationMap");
            Map<String, List<String>> detectCapabilityFieldRalationMap = new HashMap<>();

            for (String key : detectCapabilityFieldRelationMapJson.keySet()) {
                List<String> fieldList = JSON.parseArray(
                        detectCapabilityFieldRelationMapJson.getString(key),
                        String.class);
                detectCapabilityFieldRalationMap.put(key, fieldList);
            }
            result.setDetectCapabilityFieldRelationMap(detectCapabilityFieldRalationMap);

            // 解析计算类型映射
            JSONObject calculateTypesMapJson = aiRuleDescribe.getJSONObject("calculateTypesMap");
            Map<String, GetAIRuleDescribe.CalculateTypeItem> calculateTypesMap = new HashMap<>();

            for (String key : calculateTypesMapJson.keySet()) {
                GetAIRuleDescribe.CalculateTypeItem calculateTypeItem = JSON.parseObject(
                        calculateTypesMapJson.getString(key),
                        GetAIRuleDescribe.CalculateTypeItem.class);
                calculateTypesMap.put(key, calculateTypeItem);
            }
            result.setCalculateTypesMap(calculateTypesMap);

            // 解析字段类型映射
            JSONObject fieldTypeMapJson = aiRuleDescribe.getJSONObject("fieldTypeMap");
            Map<String, GetAIRuleDescribe.FieldTypeItem> fieldTypeMap = new HashMap<>();

            for (String key : fieldTypeMapJson.keySet()) {
                GetAIRuleDescribe.FieldTypeItem fieldTypeItem = JSON.parseObject(
                        fieldTypeMapJson.getString(key),
                        GetAIRuleDescribe.FieldTypeItem.class);
                fieldTypeMap.put(key, fieldTypeItem);
            }
            result.setFieldTypeMap(fieldTypeMap);

            // 填充国际化信息
            fillAIRuleI18nInfo(result);

            return setSuccess(result);
        } catch (Exception e) {
            log.error("getAIRuleDescribe error", e);
            return setFailure(ErrorCode.MODEL_DESCRIPTION_ERROR);
        }
    }

    private void loadPromptTemplate(Integer tenantId, GetAIRuleDescribe.FieldMapItem fieldMapItem, String type) {
        List<GetAIRuleDescribe.Option> options = new ArrayList<>();
        fieldMapItem.setOptions(options);

        try {
            // 获取LLM类型的提示词模板
            List<PromptTemplatePO> templates = promptTemplateDAO.getByTenantIdAndModelType(tenantId, type);

            // 如果没有找到租户特定的模板，尝试获取系统默认模板（tenantId = -1）
            if (templates == null || templates.isEmpty()) {
                templates = promptTemplateDAO.getByTenantIdAndModelType(-1, type);
            }

            // 将模板转换为选项
            if (templates != null && !templates.isEmpty()) {
                for (PromptTemplatePO template : templates) {
                    GetAIRuleDescribe.Option option = new GetAIRuleDescribe.Option();
                    option.setLabel(template.getName());
                    option.setValue(template.getCode());
                    if (template.getSupportFields() != null) {
                        option.setSupportFields(template.getSupportFields());
                    } else {
                        option.setSupportFields(new ArrayList<>());
                    }
                    options.add(option);
                }
            }
        } catch (Exception e) {
            log.error("加载提示词模板失败，tenantId: {}", tenantId, e);
        }
    }

    private void fillAIRuleI18nInfo(GetAIRuleDescribe.Result result) {
        // 处理分组标签国际化
        if (result.getGroupList() != null) {
            result.getGroupList().forEach(group ->
                    group.setLabel(I18N.getOrDefaultByOriginalKey(group.getI18nKey(), group.getLabel())));
        }

        // 处理检测能力标签国际化
        if (result.getDetectCapabilityList() != null) {
            result.getDetectCapabilityList().forEach(capability ->
                    capability.setLabel(I18N.getOrDefaultByOriginalKey(capability.getI18nKey(), capability.getLabel())));
        }

        // 处理字段映射标签国际化
        if (result.getFieldsMap() != null) {
            result.getFieldsMap().values().forEach(field ->
                    field.setLabel(I18N.getOrDefaultByOriginalKey(field.getI18nKey(), field.getLabel())));
        }

        // 处理计算类型标签国际化
        if (result.getCalculateTypesMap() != null) {
            result.getCalculateTypesMap().values().forEach(calculateType ->
                    calculateType.setLabel(I18N.getOrDefaultByOriginalKey(calculateType.getI18nKey(), calculateType.getLabel())));
        }

        // 处理字段类型标签国际化
        if (result.getFieldTypeMap() != null) {
            result.getFieldTypeMap().values().forEach(fieldType ->
                    fieldType.setLabel(I18N.getOrDefaultByOriginalKey(fieldType.getI18nKey(), fieldType.getLabel())));
        }
    }

    @Override
    public ApiResult<GetDisplayScenesByModelId.Result> getDisplayScenesByModelId(ApiArg<GetDisplayScenesByModelId.Arg> arg) {
        try {
            // 参数验证
            if (arg == null || arg.getData() == null || arg.getTenantId() == null) {
                return setFailure(ErrorCode.PARAM_EMPTY);
            }

            // 获取模型数据
            GetModelById.Result result = aiServiceAdapter.getModelById(arg.getTenantId(), arg.getData().getModelId(), false, false, false);

            if (result == null || result.getModel() == null) {
                return setFailure(1, "未找到对应模型");
            }

            // 获取模型参数
            JSONObject params = result.getModel().getParams();
            List<DisplaySceneDTO> sceneList = new ArrayList<>();

            // 处理场景映射
            if (params != null && params.containsKey("scene_map")) {
                JSONObject sceneMap = params.getJSONObject("scene_map");
                if (sceneMap != null) {
                    sceneMap.forEach((key, value) -> {
                        DisplaySceneDTO displaySceneDTO = new DisplaySceneDTO();
                        displaySceneDTO.setKey(key);
                        displaySceneDTO.setValue(String.valueOf(value));
                        displaySceneDTO.setTenantId(arg.getTenantId());
                        sceneList.add(displaySceneDTO);
                    });
                }
            }

            return setSuccess(GetDisplayScenesByModelId.Result.builder().displayScenes(sceneList).build());
        } catch (FmcgException fmcgException) {
            log.error("获取场景列表失败, modelId: {}", arg.getData().getModelId(), fmcgException);
            return setFailure(fmcgException);
        } catch (Exception e) {
            log.error("获取场景列表发生未知异常, modelId: {}", arg.getData().getModelId(), e);
            return setFailure(ErrorCode.MODEL_DETAIL_ERROR);
        }
    }

    @Override
    public ApiResult<AddToken.Result> addToken(ApiArg<AddToken.Arg> arg) {
        try {
            log.info("[ai_token] addToken,arg:{}", arg);

            // 参数校验
            arg.getData().validate();

            // 调用AI服务适配器添加Token信息
            TokenInfoDTO tokenInfo = aiServiceAdapter.addTokenInfo(arg.getTenantId(), arg.getData().getTokenInfo());

            // 构建返回结果
            AddToken.Result result = new AddToken.Result();
            result.setTokenInfo(tokenInfo);

            log.info("[ai_token] addToken,rst:{}", result);
            return setSuccess(result);
        } catch (FmcgException e) {
            log.error("addToken error", e);
            return setFailure(e);
        } catch (Exception e) {
            log.error("addToken error", e);
            return setFailure(ErrorCode.TOKEN_ADD_ERROR);
        }
    }

}
