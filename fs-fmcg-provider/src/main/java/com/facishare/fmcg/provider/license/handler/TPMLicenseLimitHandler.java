package com.facishare.fmcg.provider.license.handler;

import com.facishare.fmcg.api.service.common.RoleInitService;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.dao.po.LicensePo;
import com.facishare.fmcg.provider.impl.auth.TPMAuthServiceImpl;
import com.facishare.fmcg.provider.license.AppCodeEnum;
import com.facishare.fmcg.provider.license.LicenseCodeAppIdEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

@SuppressWarnings("Duplicates")
@Component("tpmLicenseLimitHandler")
public class TPMLicenseLimitHandler extends TPMLicenseHandler2 {
    private static final Logger log = LoggerFactory.getLogger(TPMLicenseLimitHandler.class);

    @Resource
    private RoleInitService roleInitService;
    @Resource
    private LicenseDAO licenseDAO;
    @Resource
    private TPMAuthServiceImpl tpmAuthService;

    @Override
    public String getAppCode() {
        return "FMCG.TPM_LIMIT";
    }

    @Override
    public void active(int tenantId, String tenantAccount, int userId) {
        log.info("start init tpm limit .tenantId:{}", tenantId);


        LicensePo license = licenseDAO.get(tenantId, AppCodeEnum.TPM2.code());
        if (Objects.isNull(license)) {
            log.info("init tpm limit done.");
            super.active(tenantId, tenantAccount, userId);

            LicensePo licensePo = toLicensePo(tenantId, "TENANT." + tenantId, AppCodeEnum.TPM2.code());
            log.info("add license TPM2, tenantId={}", tenantId);
            licenseDAO.add(licensePo);
        }

        try {
            tpmAuthService.createRole(String.valueOf(tenantId), "营销活动应用许可", LicenseCodeAppIdEnum.TPM_LIMIT.getCode(), LicenseCodeAppIdEnum.TPM_LIMIT.getAccessRole());
        } catch (Exception e) {
            log.info("init tpm limit createRole failed.", e);
        }

        if (Objects.nonNull(license)) {
            roleInitService.batchAddAccessRoleUser(tenantId, LicenseCodeAppIdEnum.TPM_LIMIT.getCode());
        }
    }

    private LicensePo toLicensePo(int tenantId, String owner, String appCode) {
        LicensePo po = new LicensePo();

        po.setTenantId(tenantId);
        po.setOwner(owner);
        po.setAppCode(appCode);
        po.setCode(UUID.randomUUID().toString());
        po.setCreator(-10000);
        po.setCreateTime(System.currentTimeMillis());
        po.setLastUpdater(0);
        po.setLastUpdateTime(0L);
        po.setDeleted(false);
        po.setDeleteTime(0L);

        return po;
    }
}
