package com.facishare.fmcg.provider.license.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.config.ConfigService;
import com.facishare.fmcg.adapter.exception.AdapterException;
import com.facishare.fmcg.adapter.license.LicenseAdapter;
import com.facishare.fmcg.adapter.metadata.DescribeAdapter;
import com.facishare.fmcg.adapter.metadata.dto.describe.Create;
import com.facishare.fmcg.adapter.metadata.dto.describe.GetDescribe;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.common.organization.AdminInit;
import com.facishare.fmcg.api.dto.common.organization.AppGray;
import com.facishare.fmcg.api.dto.common.organization.RecordInit;
import com.facishare.fmcg.api.dto.common.organization.RoleInit;
import com.facishare.fmcg.api.error.ErrorCode;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.common.AppGraService;
import com.facishare.fmcg.api.service.common.RecordInitService;
import com.facishare.fmcg.api.service.common.RoleInitService;
import com.facishare.fmcg.provider.business.abstraction.DescribeBusiness;
import com.facishare.fmcg.provider.business.abstraction.SalesOrderBusiness;
import com.facishare.fmcg.provider.concurrent.ParallelTaskUtil;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.dao.po.LicensePo;
import com.facishare.fmcg.provider.impl.auth.TPMDescribeServiceImpl;
import com.facishare.fmcg.provider.impl.dev.TenantDevService;
import com.facishare.fmcg.provider.license.AppCodeEnum;
import com.facishare.fmcg.provider.license.ModuleLicenseHandlerBase;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fmcg.framework.http.*;
import com.fmcg.framework.http.contract.data.auth.UpdateEntityOpenness;
import com.fmcg.framework.http.contract.integral.InitDescribe;
import com.fmcg.framework.http.contract.paas.describe.AssignRecord;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeCreate;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGet;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeRef;
import com.fmcg.framework.http.contract.tpm.ActivityTypeTemplateGet;
import com.fmcg.framework.http.contract.tpm.CommonScript;
import com.fmcg.framework.http.contract.tpm.ListActivityType;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Component("tpmCodeLicenseHandler")
public class TPMCodeLicenseHandler extends ModuleLicenseHandlerBase {
    private static final Logger log = LoggerFactory.getLogger(TPMCodeLicenseHandler.class);

    @Resource
    private DescribeAdapter describeAdapter;
    @Resource
    private RoleInitService roleInitService;
    @Resource
    private AppGraService appGraService;
    @Resource
    private DataAuthProxy dataAuthProxy;
    @Resource
    private IntegralProxy integralProxy;
    @Resource
    private PaasDescribeProxy paasDescribeProxy;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private LicenseDAO licenseDAO;
    @Resource
    private TPMProxy tpmProxy;
    @Resource
    private DescribeBusiness describeBusiness;
    @Resource
    private SalesOrderBusiness salesOrderBusiness;
    @Resource
    private LicenseAdapter licenseAdapter;
    @Resource
    private ConfigService configService;
    @Resource
    private TenantDevService tenantDevService;
    @Resource
    private TPMDescribeServiceImpl tpmDescribeService;
    @Resource
    private RecordInitService recordInitService;

    private static final String[] API_NAMES = new String[12];

    private static final Map<String, List<String>> identityIgnoreField = Maps.newHashMap();

    static {
        API_NAMES[0] = "TPMActivityUnifiedCaseObj";
        API_NAMES[1] = "TPMActivityDealerScopeObj";
        API_NAMES[2] = "TPMActivityObj";
        API_NAMES[3] = "TPMActivityStoreObj";
        API_NAMES[4] = "TPMActivityUnifiedCaseProductRangeObj";
        API_NAMES[5] = "TPMActivityProductRangeObj";
        API_NAMES[6] = "TPMActivityPrizesObj";
        API_NAMES[7] = "TPMActivityRewardDetailObj"; //
        API_NAMES[8] = "WithdrawRecordObj";
        API_NAMES[9] = "RedPacketRecordObj";
        API_NAMES[10] = "StorePromotionRecordObj"; //
        API_NAMES[11] = "RedPacketRecordDetailObj"; //

        identityIgnoreField.put("TPMActivityObj", Lists.newArrayList("budget_table", "available_amount"));
        identityIgnoreField.put("TPMActivityDetailObj", Lists.newArrayList("payment_product_unit"));
        identityIgnoreField.put("TPMActivityAgreementDetailObj", Lists.newArrayList("payment_product_unit"));
        identityIgnoreField.put("TPMActivityMaterialObj", Lists.newArrayList("unit"));
    }

    @Override
    public void active(int tenantId, String tenantAccount, int userId) {
        log.info("start init tpm code .tenantId:{}", tenantId);

        if (!licenseAdapter.validate(tenantId, "code_marketing_campaign_app")) {
            log.info("tpm code license未生效");
            throw new FmcgException("tpm code license未生效", 10000);
        }

        // 补充积分对象，码营销
        handlerIntegralObject(tenantId, Lists.newArrayList("PointsGoodsObj", "PointsExchangeRecordObj"));

        List<String> alreadyApiNames = getAlreadyApiNames(tenantId, Arrays.asList(API_NAMES));
        for (String apiName : API_NAMES) {
            if (alreadyApiNames.contains(apiName)) {
                log.info("{} describe is already", apiName);
                updateDescribe(tenantId, apiName);
                continue;
            }

            List<String> ignoreFields = identityIgnoreField.getOrDefault(apiName, Lists.newArrayList());
            try {
                Create.Arg arg = buildDescribeArg(apiName);
                JSONObject describe = JSON.parseObject(arg.getJsonData());
                ignoreFields.forEach(field -> describe.getJSONObject("fields").remove(field));
                arg.setJsonData(describe.toString());

                Create.Result result = describeAdapter.create(tenantId, arg);
                if (result.getErrCode() != 0) {
                    log.info("create describe:{} error, code : {}, message : {}", apiName, result.getErrCode(), result.getErrMessage());
                }
            } catch (AdapterException e) {
                log.error("active tpm code package fail when create describe object", e);
            }
        }
        tpmDescribeService.updateIconList(tenantId, Arrays.asList(API_NAMES));
        // 积分初始化对象
        describeBusiness.createRecordTypeLayoutForRewards(Lists.newArrayList(tenantId));

        if (isNewTenant(tenantId)) {
            addSalesOrderObjFiled(tenantId);

            addQualityGuaranteePeriod(tenantId);

            ParallelTaskUtil.ParallelTask task = ParallelTaskUtil.createParallelTask();

            task.submit(() -> objectOpenness(tenantId, Lists.newArrayList("TPMActivityObj")));

            task.submit(() -> {
                initActivityButton(tenantId);
                initActivityTemplateButton(tenantId);
            });

            //角色初始化
            log.info("initRole ");
            initRole(tenantId, tenantAccount);

            task.submit(() -> assignRecord(tenantId, "TPMActivityObj", Lists.newArrayList("00000000000000000000000000000006", "00000000000000000000000000000015"), "default__c", Lists.newArrayList("default__c")));

            task.submit(() -> {
                try {
                    openFlowLayout(tenantId);
                } catch (Exception ex) {
                    log.info("init flow layout error");
                }
            });

            task.submit(() -> reOrderScene(tenantId, -10000));
            task.run();

            ParallelTaskUtil.ParallelTask task2 = ParallelTaskUtil.createParallelTask();
            task2.submit(() -> {
                log.info("grayAndInitialization ");
                grayAndInitialization(tenantId, tenantAccount);
                log.info("updateAppAdmin ");
                updateAppAdmin(tenantId, tenantAccount);
            });
            task2.submit(() -> {
                for (String apiName : API_NAMES) {
                    try {
                        intiRecord(tenantId, apiName);
                    } catch (Exception e) {
                        log.info("initRecord error", e);
                    }
                }
            });
            task2.run();
        }

        if (!tenantDevService.isMengNiuCloudTenant(tenantId)) {
            ParallelTaskUtil.ParallelTask task3 = ParallelTaskUtil.createParallelTask();
            task3.submit(() -> {
                tryInitSystemTemplate(tenantId);
                initActivityTypeTemplate(tenantId, -10000, "reward.scan_code_get_reward");
                initActivityTypeTemplate(tenantId, -10000, "reward.stock_up_reward");
                initActivityTypeTemplate(tenantId, -10000, "reward.big_date");
            });
            task3.run();
        }
    }

    private void intiRecord(int tenantId, String apiName) {
        log.info("initRecord {}", apiName);
        ApiArg<RecordInit.Arg> arg = new ApiArg<>();
        arg.setTenantId(tenantId);
        arg.setUserId(-10000);
        RecordInit.Arg recordInitArg = new RecordInit.Arg();
        recordInitArg.setApiName(apiName);
        arg.setData(recordInitArg);
        recordInitService.initRecord(arg);
    }

    private void handlerIntegralObject(int tenantId, List<String> integralObjectNames) {

        try {
            List<String> alreadyApiNames = getAlreadyApiNames(tenantId, integralObjectNames);
            //积分对象
            // 如果对象已存在，走增量
            if (CollectionUtils.isNotEmpty(alreadyApiNames)) {
                integralObjectNames.forEach(apiName -> updateDescribe(tenantId, apiName));
            } else {
                // 走新建
                createDescribe(tenantId, integralObjectNames);
                // 新建后，补充积分对象新增字段
                integralObjectNames.forEach(apiName -> updateDescribe(tenantId, apiName));
            }
        } catch (Exception ex) {
            log.error("handlerIntegralObject error", ex);
        }

    }

    private void updateDescribe(int tenantId, String apiName) {

        if ("PointsExchangeRecordObj".equalsIgnoreCase(apiName)) {
            List<String> exchangeRecordFields = Arrays.asList("pickup_method", "reward_role", "consumer_open_id", "phone", "product_barcode",
                    "sales_store", "store_code", "store_sales_area", "store_sales_dealer_department", "delivery_company", "express_delivery_number",
                    "distribution_time", "business_id", "activity_id", "dealer_id", "goods_value", "goods_type", "prize_product_id");

            describeBusiness.addFieldForPhysicalRewards(tenantId, apiName, "pointsExchangeRecordObj", exchangeRecordFields);
        }
        if ("TPMActivityRewardDetailObj".equalsIgnoreCase(apiName)) {
            List<String> exchangeRecordFields = Arrays.asList("prize_name", "reward_person_id");
            describeBusiness.addFieldForPhysicalRewards(tenantId, apiName, "tpmActivityRewardDetailObj", exchangeRecordFields);
        }

        if ("RedPacketRecordObj".equalsIgnoreCase(apiName)) {
            describeBusiness.addFieldForPhysicalRewards(tenantId, apiName, "RedPacketRecordObj", Lists.newArrayList("activity_id"));
        }

        if ("RedPacketRecordDetailObj".equalsIgnoreCase(apiName)) {
            PaasDescribeGet.Result obj = paasDescribeProxy.get(tenantId, -10000, "RedPacketRecordDetailObj");
            PaasDescribeGet.Result FMCGSerialNumberObj = paasDescribeProxy.get(tenantId, -10000, "FMCGSerialNumberObj");
            if (obj.getCode() == 0) {
                Map<String, JSONObject> fields = obj.getData().getDescribe().getFields();

                if (!fields.containsKey("serial_number_id") && (FMCGSerialNumberObj.getCode() == 0 && Objects.nonNull(FMCGSerialNumberObj.getData()))) {
                    describeBusiness.addFieldForPhysicalRewards(tenantId, apiName, "RedPacketRecordDetailObj", Lists.newArrayList("serial_number_id"));
                    log.info("RedPacketRecordDetailObj addFieldForPhysicalRewards");
                }
            }
        }
    }

    private void createDescribe(int tenantId, List<String> apiNames) {
        // todo ，调用积分，创建对象。
        InitDescribe.Arg arg = new InitDescribe.Arg();
        arg.setObjectApiNames(apiNames);
        InitDescribe.Result result = integralProxy.initDescribe(String.valueOf(tenantId), "-10000", arg);
        if (!result.isSuccess()) {
            log.info(String.format("创建积分对象失败，message ： %s", result.getMessage()));
            throw new RuntimeException("创建积分对象失败");
        }

    }

    private void tryInitSystemTemplate(int tenantId) {
        ListActivityType.Arg arg = new ListActivityType.Arg();
        tpmProxy.activityTypeList(tenantId, -10000, arg);
    }

    @NotNull
    private List<String> getAlreadyApiNames(int tenantId, List<String> apiNames) {
        PaasDescribeRef.Result list = paasDescribeProxy.list(tenantId, -10000, apiNames);
        if (Objects.isNull(list.getData()) || CollectionUtils.isEmpty(list.getData().getDescribe())) {
            return Collections.emptyList();
        }
        return list.getData().getDescribe().stream().map(PaasDescribeRef.Describe::getApiName).collect(Collectors.toList());
    }

    private void addQualityGuaranteePeriod(int tenantId) {
        describeBusiness.addFieldAndCheckFieldIsExist(tenantId, "ProductObj", "quality_guarantee_period", false, true);
    }

    private void addSalesOrderObjFiled(Integer tenantId) {
        salesOrderBusiness.addSalesOrderTPMField(null, tenantId);
    }

    private void grayAndInitialization(int tenantId, String tenantAccount) {
        ApiArg<AppGray.Arg> arg = new ApiArg<>();
        arg.setTenantId(tenantId);
        arg.setTenantAccount(tenantAccount);
        arg.setUserId(1000);
        arg.setData(new AppGray.Arg());
        appGraService.addGrayAndInitialization(arg);
    }

    private void updateAppAdmin(int tenantId, String tenantAccount) {
        ApiArg<AdminInit.Arg> arg = new ApiArg<>();
        arg.setTenantId(tenantId);
        arg.setTenantAccount(tenantAccount);
        arg.setUserId(1000);
        arg.setData(new AdminInit.Arg());
        roleInitService.preAdmin(arg);
    }

    private void initRole(int tenantId, String tenantAccount) {
        ApiArg<RoleInit.Arg> arg = new ApiArg<>();
        arg.setTenantId(tenantId);
        arg.setTenantAccount(tenantAccount);
        arg.setUserId(1000);
        arg.setData(new RoleInit.Arg());
        roleInitService.initTPMCodeRole(arg);
        roleInitService.initTPMCodeRolePermission(arg);
    }

    private void initActivityButton(Integer tenantId) {
        CommonScript.Arg arg = new CommonScript.Arg();
        arg.setModule("init_close_activity_button");
        arg.setTenantIds(Lists.newArrayList(String.valueOf(tenantId)));
        CommonScript.Result result = tpmProxy.commonScript(tenantId, -10000, arg);
        log.info("init activity arg:{},rst:{}", arg, result);
    }

    private void initActivityTemplateButton(int tenantId) {
        CommonScript.Arg arg = new CommonScript.Arg();
        arg.setModule("init_close_activity_template_button");
        arg.setTenantIds(Lists.newArrayList(String.valueOf(tenantId)));
        CommonScript.Result result = tpmProxy.commonScript(tenantId, -10000, arg);
        log.info("init activityUnifiedCase arg:{},rst:{}", arg, result);
    }

    @Override
    public void afterEvent(int tenantId, String tenantAccount, int userId) {

        LicensePo licensePo = licenseDAO.get(tenantId, getAppCode());
        if (licensePo != null) {
            licenseDAO.updateLicenseVersion(licensePo.getId(), "2.0");
        }
    }

    @Override
    public String formatOwnerCode(int tenantId, String tenantAccount, int userId) {
        return "TENANT." + tenantId;
    }

    @Override
    public String getAppCode() {
        return "FMCG.TPM_CODE";
    }

    private Create.Arg buildDescribeArg(String apiName) {

        Create.Arg arg = new Create.Arg();
        arg.setActive(true);
        arg.setIncludeLayout(true);

        try {
            arg.setJsonData(describe(apiName));
            arg.setJsonLayout(layout(apiName));
            arg.setJsonListLayout(listLayout(apiName));
        } catch (IOException ex) {
            throw new FmcgException(ErrorCode.ARG_ERROR);
        }

        arg.setLayoutType("detail");
        return arg;
    }

    private void assignRecord(Integer tenantId, String apiName, List<String> roleIds, String defaultRecord, List<String> records) {
        AssignRecord.Arg arg = new AssignRecord.Arg();
        List<AssignRecord.Role> roles = new ArrayList<>();
        for (String roleId : roleIds) {
            AssignRecord.Role role = new AssignRecord.Role();
            role.setDefaultRecord(defaultRecord);
            role.setRoleCode(roleId);
            role.setRecords(records);
            roles.add(role);
        }
        arg.setApiName(apiName);
        arg.setRoleList(JSON.toJSONString(roles));
        AssignRecord.Result result = paasDescribeProxy.assignRecord(tenantId, -10000, arg);
        if (result.getErrCode() != 0) {
            log.info("assign record for role err.arg:{},result:{}", arg, result);
        }
    }

    private void objectOpenness(Integer tenantId, List<String> apiNames) {

        UpdateEntityOpenness.Arg arg = new UpdateEntityOpenness.Arg();
        UpdateEntityOpenness.Context context = new UpdateEntityOpenness.Context();
        arg.setEntityOpenness(Lists.newArrayList());
        context.setAppId("CRM");
        context.setTenantId(tenantId);
        context.setUserId(-10000);
        arg.setContext(context);
        apiNames.forEach(name -> {
            UpdateEntityOpenness.Entity entity = new UpdateEntityOpenness.Entity();
            entity.setApiName(name);
            entity.setAppId("CRM");
            entity.setPermission(1);
            entity.setScope(0);
            entity.setTenantId(tenantId);
            arg.getEntityOpenness().add(entity);
        });

        UpdateEntityOpenness.Result result = dataAuthProxy.updateEntityOpenness(tenantId, -10000, arg);
        if (result.getErrCode() != 0) {
            log.info("set object openness fail.arg:{},result:{}", arg, result);
        }
    }

    private String describe(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:tpm2/%s.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    private String layout(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:tpm2/%sDetailLayout.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    private String listLayout(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:tpm2/%sMobileLayout.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    public void openFlowLayout(Integer tenantId) {
        try {
            Map<String, String> configMap = configService.get("variables_fmcg_gray");
            String eiGrayList = configMap.getOrDefault("ei_flow_layout_gray", "");
            if (Strings.isNullOrEmpty(eiGrayList)) {
                throw new RuntimeException("获取灰度流程布局企业失败");
            }
            if (!eiGrayList.contains(String.valueOf(tenantId))) {
                if (eiGrayList.endsWith(",") || Strings.isNullOrEmpty(eiGrayList)) {
                    eiGrayList += "\"" + tenantId + "\"";
                } else {
                    eiGrayList += ",\"" + tenantId + "\"";
                }
                configService.update("variables_fmcg_gray", "ei_flow_layout_gray", eiGrayList);
            }

        } catch (Exception e) {
            log.error("updateFlowLayoutConfig is error tenantId:{}", tenantId, e);
        }

    }

    @Override
    public String upgrade(int tenantId) {
        this.active(tenantId, eieaConverter.enterpriseIdToAccount(tenantId), -10000);
        super.upgrade(tenantId);
        return null;
    }

    private void reOrderScene(Integer tenantId, Integer userId) {
        List<String> activityOrder = Lists.newArrayList("code", "name", "activity_type", "dealer_id", "activity_amount", "activity_actual_amount", "begin_date", "end_date", "activity_status", "multi_department_range", "closed_status", "close_time", "owner", "owner_department", "created_by", "create_time", "last_modified_by");
        describeBusiness.rebuildScene(tenantId, userId, "TPMActivityObj", activityOrder, null);
    }

    private void initActivityTypeTemplate(Integer tenantId, Integer userId, String activityTypeTemplateId) {
        ActivityTypeTemplateGet.Arg arg = new ActivityTypeTemplateGet.Arg();
        arg.setId(activityTypeTemplateId);
        ActivityTypeTemplateGet.Result result = tpmProxy.activityTypeTemplateGet(tenantId, userId, arg);
        log.info("initActivityTypeTemplate result:{}", result);
    }

    private boolean isNewTenant(Integer tenantId) {
        LicensePo license = licenseDAO.get(tenantId, AppCodeEnum.TPM.code());
        log.info("TPM CODE license tpm1 : {}", license);
        if (license == null) {
            license = licenseDAO.get(tenantId, AppCodeEnum.TPM2.code());
            log.info("TPM CODE license tpm2 : {}", license);
            return license == null;
        }
        return false;
    }
}
