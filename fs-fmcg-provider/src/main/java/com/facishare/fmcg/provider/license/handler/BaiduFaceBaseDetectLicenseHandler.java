package com.facishare.fmcg.provider.license.handler;

import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.license.LicenseAdapter;
import com.facishare.fmcg.adapter.license.dto.ValidateLicense;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.provider.impl.inner.DataUpdateService;
import com.facishare.fmcg.provider.license.ModuleLicenseHandlerBase;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGetField;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component(value = "baiduFaceBaseDetectLicenseHandler")
public class BaiduFaceBaseDetectLicenseHandler extends ModuleLicenseHandlerBase {

    @Resource
    private DataUpdateService dataUpdateService;

    @Resource
    private PaasDescribeProxy paasDescribeProxy;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private LicenseAdapter licenseAdapter;

    @Override
    public String getAppCode() {
        return "FMCG.BAIDU_FACE_BASE_DETECT";
    }

    @Override
    public void active(int tenantId, String tenantAccount, int userId) {
        super.active(tenantId, tenantAccount, userId);
        mainLogic(tenantId);
    }

    @Override
    public String upgrade(int tenantId) {
        super.upgrade(tenantId);
        return mainLogic(tenantId);
    }

    private String mainLogic(Integer tenantId) {

        if (containsLicense(tenantId, "fmcg_ai_face_recognition_app") && !isExistsField(tenantId, "PublicEmployeeObj", "ai_selfie")) {
            String baseFaceString = loadFieldFile("ai_selfie.json");
            if (baseFaceString == null) {
                throw new FmcgException("获取文件失败。", 500);
            }
            Map<String, Map<String, Map<String, Object>>> describeMap = new HashMap<>();
            Map<String, Map<String, Object>> filedMap = new HashMap<>();
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("describe",baseFaceString);
            filedMap.put("ai_selfie", paramMap);
            describeMap.put("PublicEmployeeObj", filedMap);
            String result = dataUpdateService.addDescribeField(Lists.newArrayList(tenantId), describeMap);
            log.info("add selfie field result:{}", result);
        }
        return null;
    }

    public boolean containsLicense(Integer tenantId, String licence) {

        ValidateLicense.LicenseInfo licenseInfo = licenseAdapter.get(tenantId, licence);

        return licenseInfo != null;
    }

    public boolean isExistsField(Integer tenantId, String describeApiName, String fieldApiName) {
        PaasDescribeGetField.Arg arg = new PaasDescribeGetField.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setFieldApiName(fieldApiName);
        PaasDescribeGetField.Result result = paasDescribeProxy.findCustomFieldDescribe(tenantId, -10000, arg);
        return result.getErrCode() == 0;
    }


    private String loadFieldFile(String fileName) {
        File file = null;
        try {
            file = ResourceUtils.getFile(String.format("classpath:face/%s", fileName));
            return new String(Files.readAllBytes(file.toPath()));
        } catch (Exception e) {
            log.error("get file err:{}", fileName);
        }
        return null;
    }

    @Override
    public void invalid(int tenantId, String tenantAccount, int userId) {
        super.invalid(tenantId, tenantAccount, userId);
    }
}
