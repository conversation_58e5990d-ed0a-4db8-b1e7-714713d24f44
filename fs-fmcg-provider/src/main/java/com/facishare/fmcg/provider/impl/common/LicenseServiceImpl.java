package com.facishare.fmcg.provider.impl.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fmcg.adapter.checkins.CheckInsAdapter;
import com.facishare.fmcg.adapter.checkins.dto.ModuleInfoDto;
import com.facishare.fmcg.adapter.license.LicenseAdapter;
import com.facishare.fmcg.adapter.license.dto.ValidateLicense;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.common.license.*;
import com.facishare.fmcg.api.dto.common.license.model.LicenseDto;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.common.LicenseService;
import com.facishare.fmcg.provider.abstraction.ServiceBase;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.dao.po.LicensePo;
import com.facishare.fmcg.provider.license.IModuleLicenseHandler;
import com.facishare.fmcg.provider.license.ModuleLicenseHandlerFactory;
import com.github.jedis.support.JedisCmd;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class LicenseServiceImpl extends ServiceBase implements LicenseService {

    @Resource
    private LicenseDAO licenseDAO;

    @Resource
    private LicenseAdapter licenseAdapter;

    @Resource
    private CheckInsAdapter checkinsAdapter;


    @Resource
    private JedisCmd redisCmd;

    @Override
    public ApiResult<Active.Result> active(ApiArg<Active.Arg> arg) {
        IModuleLicenseHandler center = ModuleLicenseHandlerFactory.getCenter(arg.getData().getAppCode());
        String owner = center.formatOwnerCode(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId());
        LicensePo po = licenseDAO.get(arg.getTenantId(), owner, arg.getData().getAppCode());

        if (po != null) {
            return setSuccess(Active.Result.builder().license(toLicenseDto(po)).build());
        }

        try {
            center.active(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId());
            po = toLicensePo(
                    arg.getTenantId(),
                    arg.getUserId(),
                    owner,
                    arg.getData().getAppCode());

            if ("FMCG.TPM_BUDGET".equals(arg.getData().getAppCode()) || "FMCG.TPM_BUDGET.2".equals(arg.getData().getAppCode())) {
                redisCmd.del(String.format("BUDGET_LICENSE_OPEN_CACHE:%s", arg.getTenantId()));
            }

            log.info("add license active:{}", JSON.toJSONString(arg));
            licenseDAO.add(po);
            center.afterEvent(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId());
        } catch (FmcgException fmcgException) {
            log.info("active error, e", fmcgException);
            throw fmcgException;
        }

        return setSuccess(Active.Result.builder().license(toLicenseDto(po)).build());
    }

    @Override
    public ApiResult<Upgrade.Result> upgrade(ApiArg<Upgrade.Arg> arg) {
        IModuleLicenseHandler center = ModuleLicenseHandlerFactory.getCenter(arg.getData().getAppCode());
        if (-10000 == arg.getTenantId()) {
            List<LicensePo> licenses = licenseDAO.query(arg.getData().getAppCode());
            for (LicensePo license : licenses) {
                center.upgrade(license.getTenantId());
            }
        } else {
            center.upgrade(arg.getTenantId());
        }
        if ("FMCG.TPM_BUDGET".equals(arg.getData().getAppCode()) || "FMCG.TPM_BUDGET.2".equals(arg.getData().getAppCode())) {
            redisCmd.del(String.format("BUDGET_LICENSE_OPEN_CACHE:%s", arg.getTenantId()));
        }
        return setSuccess(Upgrade.Result.builder().success(true).build());
    }

    @Override
    public ApiResult<Invalid.Result> invalid(ApiArg<Invalid.Arg> arg) {
        IModuleLicenseHandler center = ModuleLicenseHandlerFactory.getCenter(arg.getData().getAppCode());
        String owner = center.formatOwnerCode(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId());
        center.invalid(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId());
        licenseDAO.invalid(arg.getTenantId(), arg.getUserId(), owner, arg.getData().getAppCode());
        return setSuccess(Invalid.Result.builder().build());
    }

    @Override
    public ApiResult<Validate.Result> validate(ApiArg<Validate.Arg> arg) {
        Validate.Result result = new Validate.Result();
        result.setActivated(ModuleLicenseHandlerFactory.getCenter(arg.getData().getAppCode()).validate(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId()));
        return setSuccess(result);
    }

    @Override
    public ApiResult<Get.Result> get(ApiArg<Get.Arg> arg) {
        log.info("get license:{}", JSON.toJSONString(arg));
        String owner = ModuleLicenseHandlerFactory.getCenter(arg.getData().getAppCode()).formatOwnerCode(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId());
        LicenseDto license = toLicenseDto(licenseDAO.get(arg.getTenantId(), owner, arg.getData().getAppCode()));

        if (arg.getData().getAppCode().equals("FMCG.TPM") || arg.getData().getAppCode().equals("FMCG.TPM.2")) {
            ValidateLicense.LicenseInfo tpmLicenseInfo = licenseAdapter.get(arg.getTenantId(), "trade_promotion_management_app");
            if (tpmLicenseInfo == null) {
                tpmLicenseInfo = licenseAdapter.get(arg.getTenantId(), "fmcg_tpm_app");
            }

            JSONObject externalData = new JSONObject();
            externalData.put("type", "paas_license");
            if (tpmLicenseInfo == null) {
                externalData.put("license_status", "invalid");
            } else {
                externalData.put("begin_time", tpmLicenseInfo.getStartTime());
                externalData.put("end_time", tpmLicenseInfo.getExpiredTime());
                if (System.currentTimeMillis() < tpmLicenseInfo.getExpiredTime()) {
                    externalData.put("license_status", "active");
                } else {
                    externalData.put("license_status", "expired");
                }
            }
            return setSuccess(Get.Result.builder().license(license).externalData(externalData).build());
        }
        return setSuccess(Get.Result.builder().license(license).build());
    }

    @Override
    public ApiResult<BatchValidate.Result> batchValidate(ApiArg<BatchValidate.Arg> arg) {
        BatchValidate.Result result = new BatchValidate.Result();
        result.setData(Lists.newArrayList());

        if (arg.getData().getAppCodeList() == null || arg.getData().getAppCodeList().isEmpty()) {
            arg.getData().setAppCodeList(Lists.newArrayList(ModuleLicenseHandlerFactory.getAllHandlerMap().keySet()));
        }

        for (String appCode : arg.getData().getAppCodeList()) {
            BatchValidate.ActiveStatus status = new BatchValidate.ActiveStatus();
            status.setAppCode(appCode);
            status.setActivated(ModuleLicenseHandlerFactory.getCenter(appCode).validate(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId()));
            result.getData().add(status);
        }
        return setSuccess(result);
    }

    @Override
    public ApiResult<QueryAll.Result> queryAllTenant(ApiArg<QueryAll.Arg> arg) {
        List<String> appCodes = arg.getData().getAppCodes();

        if (CollectionUtils.isEmpty(appCodes)) {
            return setSuccess(QueryAll.Result.builder().licenses(Lists.newArrayList()).build());
        }

        List<LicensePo> licenses = licenseDAO.queryAllTPM(appCodes);

        List<LicenseDto> licenseDtos = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(licenses)) {
            licenseDtos.addAll(licenses.stream().map(this::toLicenseDto).collect(Collectors.toList()));
        }
        return setSuccess(QueryAll.Result.builder().licenses(licenseDtos).build());
    }

    @Override
    public ApiResult<Add.Result> add(ApiArg<Add.Arg> arg) {
        Integer tenantId = arg.getTenantId();
        LicensePo licensePo = toLicensePo(tenantId, -10000, "TENANT." + tenantId, arg.getData().getAppCode());
        String id = licenseDAO.add(licensePo);
        return setSuccess(Add.Result.builder().id(id).build());
    }

    @Override
    public ApiResult<Active.Result> activeAll(ApiArg<Active.Arg> arg) {
        IModuleLicenseHandler center = ModuleLicenseHandlerFactory.getCenter(arg.getData().getAppCode());
        String owner = center.formatOwnerCode(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId());
        LicensePo po = licenseDAO.get(arg.getTenantId(), owner, arg.getData().getAppCode());
        if (po != null) {
            log.info("add license active:{}", JSON.toJSONString(arg));
            licenseDAO.add(po);
        }

        center.active(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId());
        po = toLicensePo(
                arg.getTenantId(),
                arg.getUserId(),
                owner,
                arg.getData().getAppCode());

        if ("FMCG.TPM_BUDGET".equals(arg.getData().getAppCode()) || "FMCG.TPM_BUDGET.2".equals(arg.getData().getAppCode())) {
            redisCmd.del(String.format("BUDGET_LICENSE_OPEN_CACHE:%s", arg.getTenantId()));
        }

        center.afterEvent(arg.getTenantId(), arg.getTenantAccount(), arg.getUserId());
        return setSuccess(Active.Result.builder().license(toLicenseDto(po)).build());
    }

    @Override
    public ApiResult<Dashboard.Result> fmcgDashboard(ApiArg<Dashboard.Arg> arg) {
        Dashboard.Result result = new Dashboard.Result();

        boolean appFlag0 = licenseAdapter.validate(arg.getTenantId(), "kx_industry");
        boolean appFlag1 = licenseAdapter.validate(arg.getTenantId(), "fmcg_package");

        boolean appFlag = appFlag0 || appFlag1;

        if (!appFlag) {
            result.setApps(Lists.newArrayList());
            result.setStatus(0);
            return setSuccess(result);
        }

        result.setStatus(1);
        result.setApps(Lists.newArrayList());

        Dashboard.IndustryApp app = new Dashboard.IndustryApp();
        app.setCode("kx_industry");
        app.setLabel("快销行业套件");
        app.setProducts(Lists.newArrayList());

        ModuleInfoDto moduleInfo = checkinsAdapter.getAppInfo(arg.getTenantAccount());

        if (moduleInfo != null && moduleInfo.isHasSupperCheckIns()) {
            Dashboard.IndustryProduct product0 = new Dashboard.IndustryProduct();
            product0.setCode("advanced_outwork_app");
            product0.setLabel("高级外勤");
            product0.setPreview_mode("local");
            product0.setModules(Lists.newArrayList());

            Dashboard.IndustryModule module1 = new Dashboard.IndustryModule();
            module1.setCode("action_type_role_module");
            module1.setLabel("外勤动作、外勤类型、外勤规则");
            module1.setDescription("可给不同部门的员工配置不同的外勤动作、外勤类型及外勤规则");
            module1.setStatus(moduleInfo.getActionTotal() > 0 || moduleInfo.getCheckTypeTotal() > 0 || moduleInfo.getRuleTotal() > 0 ? 2 : 1);
            module1.setNavigate("foo");
            module1.setPreview(new HashMap<>(3));
            module1.getPreview().put("action_count", moduleInfo.getActionTotal());
            module1.getPreview().put("type_count", moduleInfo.getCheckTypeTotal());
            module1.getPreview().put("role_count", moduleInfo.getRuleTotal());
            product0.getModules().add(module1);

            Dashboard.IndustryModule module2 = new Dashboard.IndustryModule();
            module2.setCode("route_module");
            module2.setLabel("外勤路线");
            module2.setDescription("支持为员工设置外勤路线，合理的规划外勤计划，便于路线拜访");
            module2.setStatus(moduleInfo.getRouteTotal() > 0 ? 2 : 1);
            module2.setNavigate("foo");
            module2.setPreview(new HashMap<>(1));
            module2.getPreview().put("route_count", moduleInfo.getRouteTotal());
            product0.getModules().add(module2);

            Dashboard.IndustryModule module3 = new Dashboard.IndustryModule();
            module3.setCode("care_sales_module");
            module3.setLabel("车销");
            module3.setDescription("支持车销人员便捷使用装车，卸车能力，提供建议装车量");
            module3.setStatus(1);
            module3.setNavigate("foo");
            module3.setPreview(new HashMap<>(0));
            product0.getModules().add(module3);

            app.getProducts().add(product0);
        }

        Dashboard.IndustryProduct product1 = new Dashboard.IndustryProduct();
        product1.setCode("stock_control_app");
        product1.setLabel("库存管理");
        product1.setPreview_mode("external");
        product1.setModules(Lists.newArrayList());

        Dashboard.IndustryModule module10 = new Dashboard.IndustryModule();
        module10.setCode("delivery_note_module");
        module10.setLabel("发货单");
        product1.getModules().add(module10);

        Dashboard.IndustryModule module11 = new Dashboard.IndustryModule();
        module11.setCode("stock_module");
        module11.setLabel("库存");
        product1.getModules().add(module11);

        Dashboard.IndustryModule module12 = new Dashboard.IndustryModule();
        module12.setCode("batch_and_serial_number_module");
        module12.setLabel("批次和序列号管理");
        product1.getModules().add(module12);

        app.getProducts().add(product1);

        Dashboard.IndustryProduct product2 = new Dashboard.IndustryProduct();
        product2.setCode("promotion_app");
        product2.setLabel("促销设置");
        product2.setPreview_mode("external");
        product2.setModules(Lists.newArrayList());

        Dashboard.IndustryModule module20 = new Dashboard.IndustryModule();
        module20.setCode("promotion_module");
        module20.setLabel("促销设置");
        product2.getModules().add(module20);

        app.getProducts().add(product2);

        Dashboard.IndustryProduct product3 = new Dashboard.IndustryProduct();
        product3.setCode("service_dds_parent_app");
        product3.setLabel("渠道通路管理");
        product3.setPreview_mode("external");
        product3.setModules(Lists.newArrayList());

        Dashboard.IndustryModule module30 = new Dashboard.IndustryModule();
        module30.setCode("service_dds_parent_module");
        module30.setLabel("渠道通路管理");
        product3.getModules().add(module30);

        app.getProducts().add(product3);

        result.getApps().add(app);

        return setSuccess(result);
    }

    private LicenseDto toLicenseDto(LicensePo po) {
        if (po == null) {
            return null;
        }
        LicenseDto dto = new LicenseDto();

        dto.setTenantId(po.getTenantId());
        dto.setOwner(po.getOwner());
        dto.setAppCode(po.getAppCode());
        dto.setCreator(po.getCreator());
        dto.setCreateTime(po.getCreateTime());
        dto.setLastUpdater(po.getLastUpdater());
        dto.setLastUpdateTime(po.getLastUpdateTime());
        dto.setDeleted(po.isDeleted());
        dto.setDeleteTime(po.getDeleteTime());
        dto.setCode(po.getCode());
        dto.setLicenseVersion(po.getLicenseVersion());

        return dto;
    }

    private LicensePo toLicensePo(int tenantId, Integer employeeId, String owner, String appCode) {
        LicensePo po = new LicensePo();

        po.setTenantId(tenantId);
        po.setOwner(owner);
        po.setAppCode(appCode);
        po.setCode(UUID.randomUUID().toString());
        po.setCreator(employeeId);
        po.setCreateTime(System.currentTimeMillis());
        po.setLastUpdater(0);
        po.setLastUpdateTime(0L);
        po.setDeleted(false);
        po.setDeleteTime(0L);

        return po;
    }
}