<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:META-INF/dao.xml"/>
    <import resource="classpath:META-INF/adapter.xml"/>
    <import resource="classpath:META-INF/adapter-dubbo.xml"/>
    <import resource="classpath:spring/ei-ea-converter.xml"/>
    <import resource="classpath:spring/license-client.xml"/>
    <import resource="classpath:spring/license-client.xml"/>
    <import resource="classpath:fmcg-sdk-ai.xml"/>
    <import resource="classpath:fmcg-framework-http.xml"/>
    <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>
    <import resource="classpath:fs-uc-rest.xml"/>
    <import resource="classpath:META-INF/spring/fs-webpage-customer-rest-api.xml"/>

    <!--  fmcg module license handler  -->
    <bean id="ddsLicenseCenter" class="com.facishare.fmcg.provider.license.handler.DdsLicenseHandler"/>
    <bean id="coinLicenseCenter" class="com.facishare.fmcg.provider.license.handler.CoinLicenseHandler"/>
    <bean id="efficiencyLicenseCenter" class="com.facishare.fmcg.provider.license.handler.EfficiencyLicenseHandler"/>
    <bean id="integralLicenseCenter" class="com.facishare.fmcg.provider.license.handler.IntegralLicenseHandler"/>
    <bean id="dealerLicenseCenter" class="com.facishare.fmcg.provider.license.handler.DealerLicenseHandler"/>
    <bean id="dataSyncLicenseCenter" class="com.facishare.fmcg.provider.license.handler.DataSyncLicenseHandler"/>

    <!--  local service  -->
    <bean id="licenseService" class="com.facishare.fmcg.provider.impl.common.LicenseServiceImpl"/>
    <bean id="organizationServiceImpl" class="com.facishare.fmcg.provider.impl.common.OrganizationServiceImpl"/>
    <bean id="carSalesMemberService" class="com.facishare.fmcg.provider.impl.carsales.CarSalesMemberServiceImpl"/>
    <bean id="aiServiceRecordService" class="com.facishare.fmcg.provider.impl.record.AIServiceRecordServiceImpl"/>
    <bean id="tenantConfigService" class="com.facishare.fmcg.provider.impl.common.TenantConfigServiceImpl"/>
    <bean id="carSalesService" class="com.facishare.fmcg.provider.impl.carsales.CarSalesServiceImpl"/>
    <bean id="warehouseService" class="com.facishare.fmcg.provider.impl.carsales.WarehouseServiceImpl"/>
    <bean id="faceService" class="com.facishare.fmcg.provider.impl.ai.face.FaceServiceImpl"/>
    <bean id="customerOpeningService" class="com.facishare.fmcg.provider.co.service.impl.CustomerOpeningServiceImpl"/>

    <bean id="springContextHolder" class="com.facishare.fmcg.provider.license.SpringContextHolder" lazy-init="false"/>

    <!--  redis  -->
    <bean id="redisCmd" class="com.github.jedis.support.JedisFactoryBean" p:configName="checkins-v2-redis"/>

    <!--  remote apis  -->
    <bean id="qxEIEAConverter" class="com.facishare.qixin.converter.QXEIEAConverterImpl"/>

    <bean id="excelImportFactory" class="com.facishare.fmcg.provider.co.excel.ExcelImportFactory"/>
    <bean id="bdmExcelImport" class="com.facishare.fmcg.provider.co.excel.impl.BDMExcelImport"/>

    <!--  log platform  -->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
    <bean id="okClientSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="fs-fmcg-framework-config"/>

</beans>
