<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

    <bean id="dbContext" class="com.github.mongo.support.MongoDataStoreFactoryBean" p:configName="fs-fmcg-mongo"/>
    <bean id="customDbContext" class="com.github.mongo.support.MongoDataStoreFactoryBean"
          p:configName="fs-fmcg-customized-mongo"/>

    <bean id="licenseDAO" class="com.facishare.fmcg.provider.dao.impl.LicenseDAOImpl"/>
    <bean id="routeRecordDAO" class="com.facishare.fmcg.provider.dao.impl.RouteRecordDAOImpl"/>
    <bean id="tenantConfigDAO" class="com.facishare.fmcg.provider.dao.impl.TenantConfigDAOImpl"/>
    <bean id="carSalesMemberDAO" class="com.facishare.fmcg.provider.dao.impl.CarSalesMemberDAOImpl"/>
    <bean id="devicePhotoDetailDAO" class="com.facishare.fmcg.provider.dao.impl.DevicePhotoDetailDAOImpl"/>
    <bean id="promptTemplateDAO" class="com.facishare.fmcg.provider.dao.impl.PromptTemplateDAOImpl"/>
    <bean id="aiServiceRecordDAO" class="com.facishare.fmcg.provider.dao.impl.AIServiceRecordDAOImpl"/>
</beans>